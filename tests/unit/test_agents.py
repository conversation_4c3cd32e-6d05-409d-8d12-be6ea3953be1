"""
Unit tests for the Mavia Math Tutor agents.
"""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock
from google.adk.sessions import Session
from google.adk.agents.invocation_context import InvocationContext

from src.mavia_tutor.agents import (
    TutorAgent, 
    LearningPathAgent, 
    TestingAgent, 
    TopicExplainerAgent
)
from src.mavia_tutor.models.curriculum import Curriculum
from src.mavia_tutor.utils import CurriculumLoader


@pytest.fixture
def sample_curriculum():
    """Create a sample curriculum for testing."""
    loader = CurriculumLoader()
    return loader.load_default_curriculum()


@pytest.fixture
def sample_session_state():
    """Create sample session state for testing."""
    return {
        "student_id": "test_student",
        "student_profile": {
            "student_id": "test_student",
            "name": "Test Student",
            "math_level": "beginner",
            "learning_style": "visual",
            "goals": ["Learn fractions"],
            "strengths": ["Visual learning"],
            "challenges": ["Word problems"]
        },
        "student_progress": {
            "topics_progress": {}
        },
        "session_goals": ["Practice basic arithmetic"]
    }


class TestLearningPathAgent:
    """Test cases for the Learning Path Agent."""
    
    def test_initialization(self, sample_curriculum):
        """Test agent initialization."""
        agent = LearningPathAgent(curriculum=sample_curriculum)
        
        assert agent.name == "LearningPathAgent"
        assert agent.curriculum == sample_curriculum
        assert agent.output_key == "generated_learning_path"
    
    @pytest.mark.asyncio
    async def test_learning_path_generation(self, sample_curriculum, sample_session_state):
        """Test learning path generation."""
        agent = LearningPathAgent(curriculum=sample_curriculum)
        
        # Create mock session and context
        session = Session()
        for key, value in sample_session_state.items():
            session.state[key] = value
        
        context = InvocationContext(session=session)
        
        # Run agent
        events = []
        async for event in agent._run_async_impl(context):
            events.append(event)
        
        # Verify results
        assert len(events) > 0
        assert "generated_learning_path" in session.state
        
        learning_path = session.state["generated_learning_path"]
        assert "path_id" in learning_path
        assert "topic_sequence" in learning_path
        assert len(learning_path["topic_sequence"]) > 0


class TestTestingAgent:
    """Test cases for the Testing Agent."""
    
    def test_initialization(self):
        """Test agent initialization."""
        agent = TestingAgent()
        
        assert agent.name == "TestingAgent"
        assert agent.output_key == "knowledge_rating"
    
    @pytest.mark.asyncio
    async def test_assessment_generation(self, sample_session_state):
        """Test assessment generation."""
        agent = TestingAgent()
        
        # Create mock session and context
        session = Session()
        for key, value in sample_session_state.items():
            session.state[key] = value
        
        session.state["current_topic_id"] = "basic_arithmetic"
        context = InvocationContext(session=session)
        
        # Run agent
        events = []
        async for event in agent._run_async_impl(context):
            events.append(event)
        
        # Verify results
        assert len(events) > 0
        assert "current_assessment" in session.state
        
        assessment = session.state["current_assessment"]
        assert "assessment_id" in assessment
        assert "questions" in assessment
        assert len(assessment["questions"]) > 0
    
    @pytest.mark.asyncio
    async def test_knowledge_evaluation(self, sample_session_state):
        """Test knowledge evaluation."""
        agent = TestingAgent()
        
        # Create mock session and context with assessment and responses
        session = Session()
        for key, value in sample_session_state.items():
            session.state[key] = value
        
        session.state["current_topic_id"] = "basic_arithmetic"
        session.state["current_assessment"] = {
            "assessment_id": "test_assessment",
            "topic_id": "basic_arithmetic",
            "questions": [
                {
                    "question_id": "q1",
                    "correct_answer": "42",
                    "points": 1
                }
            ]
        }
        session.state["student_responses"] = {
            "q1": "42"
        }
        
        context = InvocationContext(session=session)
        
        # Run agent
        events = []
        async for event in agent._run_async_impl(context):
            events.append(event)
        
        # Verify results
        assert len(events) > 0
        assert "current_knowledge_rating" in session.state
        
        rating = session.state["current_knowledge_rating"]
        assert "knowledge_level" in rating
        assert "confidence" in rating
        assert "needs_learning" in rating


class TestTopicExplainerAgent:
    """Test cases for the Topic Explainer Agent."""
    
    def test_initialization(self):
        """Test agent initialization."""
        agent = TopicExplainerAgent()
        
        assert agent.name == "TopicExplainerAgent"
        assert agent.output_key == "topic_explanation"
    
    @pytest.mark.asyncio
    async def test_topic_explanation(self, sample_session_state):
        """Test topic explanation generation."""
        agent = TopicExplainerAgent()
        
        # Create mock session and context
        session = Session()
        for key, value in sample_session_state.items():
            session.state[key] = value
        
        session.state["current_topic_id"] = "basic_arithmetic"
        session.state["current_knowledge_rating"] = {
            "knowledge_level": "basic",
            "evidence": ["Some understanding shown"],
            "recommended_focus": ["Practice more examples"]
        }
        
        context = InvocationContext(session=session)
        
        # Run agent
        events = []
        async for event in agent._run_async_impl(context):
            events.append(event)
        
        # Verify results
        assert len(events) > 0
        assert "topic_explanation" in session.state
        
        explanation = session.state["topic_explanation"]
        assert "topic_id" in explanation
        assert "explanation_content" in explanation
        assert "practice_problems" in explanation


class TestTutorAgent:
    """Test cases for the Tutor Agent (orchestrator)."""
    
    def test_initialization(self, sample_curriculum):
        """Test agent initialization."""
        agent = TutorAgent(curriculum=sample_curriculum)
        
        assert agent.name == "TutorAgent"
        assert agent.curriculum == sample_curriculum
        assert len(agent.sub_agents) == 3  # Learning path, testing, explainer
    
    @pytest.mark.asyncio
    async def test_session_initialization(self, sample_curriculum, sample_session_state):
        """Test session initialization."""
        agent = TutorAgent(curriculum=sample_curriculum)
        
        # Create mock session and context
        session = Session()
        for key, value in sample_session_state.items():
            session.state[key] = value
        
        context = InvocationContext(session=session)
        
        # Run agent for initialization
        events = []
        event_count = 0
        async for event in agent._run_async_impl(context):
            events.append(event)
            event_count += 1
            if event_count >= 3:  # Limit events for testing
                break
        
        # Verify session state was initialized
        assert "session_state" in session.state
        session_state = session.state["session_state"]
        assert "session_id" in session_state
        assert "current_phase" in session_state
        assert session_state["tutor_agent_state"] == "active"


@pytest.mark.asyncio
async def test_agent_coordination(sample_curriculum, sample_session_state):
    """Test coordination between agents."""
    # This is an integration test that verifies agents work together
    
    # Initialize agents
    tutor_agent = TutorAgent(curriculum=sample_curriculum)
    
    # Create session
    session = Session()
    for key, value in sample_session_state.items():
        session.state[key] = value
    
    context = InvocationContext(session=session)
    
    # Run tutor agent briefly to test coordination
    events = []
    event_count = 0
    async for event in tutor_agent._run_async_impl(context):
        events.append(event)
        event_count += 1
        if event_count >= 5:  # Limit for testing
            break
    
    # Verify basic coordination
    assert len(events) > 0
    assert any("session_state" in session.state for _ in [True])  # Session state created
    
    # Verify agent states are being managed
    if "session_state" in session.state:
        session_state = session.state["session_state"]
        assert "tutor_agent_state" in session_state
