"""
Unit tests for the Mavia Math Tutor data models.
"""

import pytest
from datetime import datetime
from src.mavia_tutor.models.student import (
    Student, StudentProfile, LearningProgress, TopicProgress, 
    MathLevel, LearningStyle
)
from src.mavia_tutor.models.curriculum import (
    Curriculum, Topic, LearningPath, TopicCategory, <PERSON>fficulty
)
from src.mavia_tutor.models.assessment import (
    Question, Assessment, TestResult, KnowledgeRating, 
    QuestionType, KnowledgeLevel
)
from src.mavia_tutor.models.session import (
    TutoringSession, SessionState, SessionPhase, AgentState
)


class TestStudentModels:
    """Test cases for student-related models."""
    
    def test_student_profile_creation(self):
        """Test student profile creation."""
        profile = StudentProfile(
            student_id="test_001",
            name="Test Student",
            age=12,
            grade_level="7th",
            math_level=MathLevel.ELEMENTARY,
            learning_style=LearningStyle.VISUAL,
            goals=["Learn fractions", "Improve algebra"],
            strengths=["Visual learning"],
            challenges=["Word problems"]
        )
        
        assert profile.student_id == "test_001"
        assert profile.name == "Test Student"
        assert profile.math_level == MathLevel.ELEMENTARY
        assert profile.learning_style == LearningStyle.VISUAL
        assert len(profile.goals) == 2
    
    def test_topic_progress_tracking(self):
        """Test topic progress tracking."""
        progress = TopicProgress(
            topic_id="fractions_intro",
            topic_name="Introduction to Fractions",
            mastery_level=0.7,
            attempts=5,
            correct_answers=3,
            time_spent_minutes=45.5
        )
        
        assert progress.topic_id == "fractions_intro"
        assert progress.mastery_level == 0.7
        assert progress.attempts == 5
        assert progress.correct_answers == 3
    
    def test_student_creation(self):
        """Test complete student creation."""
        profile = StudentProfile(
            student_id="test_002",
            name="Jane Doe",
            math_level=MathLevel.BEGINNER
        )
        
        progress = LearningProgress(student_id="test_002")
        
        student = Student(profile=profile, progress=progress)
        
        assert student.profile.student_id == "test_002"
        assert student.progress.student_id == "test_002"
        assert student.get_mastery_level("unknown_topic") == 0.0
    
    def test_student_progress_update(self):
        """Test updating student progress."""
        profile = StudentProfile(
            student_id="test_003",
            name="John Smith",
            math_level=MathLevel.INTERMEDIATE
        )
        
        progress = LearningProgress(student_id="test_003")
        student = Student(profile=profile, progress=progress)
        
        # Update progress for a topic
        student.update_topic_progress("algebra_basics", "Basic Algebra", True, 30.0)
        
        assert "algebra_basics" in student.progress.topics_progress
        topic_progress = student.progress.topics_progress["algebra_basics"]
        assert topic_progress.attempts == 1
        assert topic_progress.correct_answers == 1
        assert topic_progress.time_spent_minutes == 30.0
        assert topic_progress.mastery_level > 0.0


class TestCurriculumModels:
    """Test cases for curriculum-related models."""
    
    def test_topic_creation(self):
        """Test topic creation."""
        topic = Topic(
            topic_id="test_topic",
            name="Test Topic",
            description="A test topic for unit testing",
            category=TopicCategory.ARITHMETIC,
            difficulty=Difficulty.MEDIUM,
            prerequisites=["basic_arithmetic"],
            learning_objectives=["Understand test concepts"],
            key_concepts=["Testing", "Validation"],
            estimated_time_minutes=60
        )
        
        assert topic.topic_id == "test_topic"
        assert topic.category == TopicCategory.ARITHMETIC
        assert topic.difficulty == Difficulty.MEDIUM
        assert len(topic.prerequisites) == 1
    
    def test_topic_prerequisites(self):
        """Test topic prerequisite checking."""
        topic = Topic(
            topic_id="advanced_topic",
            name="Advanced Topic",
            description="An advanced topic",
            category=TopicCategory.ALGEBRA,
            difficulty=Difficulty.HARD,
            prerequisites=["basic_arithmetic", "fractions_intro"]
        )
        
        # Test with no mastered topics
        assert not topic.has_prerequisites_met(set())
        
        # Test with partial prerequisites
        assert not topic.has_prerequisites_met({"basic_arithmetic"})
        
        # Test with all prerequisites
        assert topic.has_prerequisites_met({"basic_arithmetic", "fractions_intro"})
        
        # Test with extra topics
        assert topic.has_prerequisites_met({"basic_arithmetic", "fractions_intro", "extra_topic"})
    
    def test_learning_path_creation(self):
        """Test learning path creation."""
        path = LearningPath(
            path_id="test_path",
            name="Test Learning Path",
            description="A test learning path",
            target_level="beginner",
            topic_sequence=["topic1", "topic2", "topic3"],
            estimated_duration_hours=2.5
        )
        
        assert path.path_id == "test_path"
        assert len(path.topic_sequence) == 3
        assert path.estimated_duration_hours == 2.5
    
    def test_learning_path_progress(self):
        """Test learning path progress tracking."""
        path = LearningPath(
            path_id="progress_test",
            name="Progress Test Path",
            description="Testing progress",
            target_level="intermediate",
            topic_sequence=["topic1", "topic2", "topic3", "topic4"],
            estimated_duration_hours=4.0
        )
        
        # Test with no completed topics
        assert path.get_next_topic(set()) == "topic1"
        assert path.get_progress_percentage(set()) == 0.0
        
        # Test with some completed topics
        completed = {"topic1", "topic2"}
        assert path.get_next_topic(completed) == "topic3"
        assert path.get_progress_percentage(completed) == 50.0
        
        # Test with all topics completed
        all_completed = {"topic1", "topic2", "topic3", "topic4"}
        assert path.get_next_topic(all_completed) is None
        assert path.get_progress_percentage(all_completed) == 100.0
    
    def test_curriculum_creation(self):
        """Test curriculum creation and management."""
        # Create topics
        topic1 = Topic(
            topic_id="topic1",
            name="Topic 1",
            description="First topic",
            category=TopicCategory.ARITHMETIC,
            difficulty=Difficulty.EASY
        )
        
        topic2 = Topic(
            topic_id="topic2",
            name="Topic 2", 
            description="Second topic",
            category=TopicCategory.ARITHMETIC,
            difficulty=Difficulty.MEDIUM,
            prerequisites=["topic1"]
        )
        
        # Create learning path
        path = LearningPath(
            path_id="path1",
            name="Test Path",
            description="Test learning path",
            target_level="beginner",
            topic_sequence=["topic1", "topic2"],
            estimated_duration_hours=2.0
        )
        
        # Create curriculum
        curriculum = Curriculum(
            curriculum_id="test_curriculum",
            name="Test Curriculum",
            description="A test curriculum",
            topics={"topic1": topic1, "topic2": topic2},
            learning_paths={"path1": path}
        )
        
        assert curriculum.get_topic("topic1") == topic1
        assert curriculum.get_learning_path("path1") == path
        assert curriculum.get_topic("nonexistent") is None
        
        # Test available topics
        available = curriculum.get_available_topics(set())
        assert len(available) == 1  # Only topic1 has no prerequisites
        assert available[0].topic_id == "topic1"
        
        available_after = curriculum.get_available_topics({"topic1"})
        assert len(available_after) == 2  # Both topics available now


class TestAssessmentModels:
    """Test cases for assessment-related models."""
    
    def test_question_creation(self):
        """Test question creation."""
        question = Question(
            question_id="q1",
            topic_id="arithmetic",
            question_type=QuestionType.MULTIPLE_CHOICE,
            difficulty="easy",
            question_text="What is 2 + 2?",
            options=["3", "4", "5", "6"],
            correct_answer="4",
            explanation="2 + 2 equals 4",
            points=1
        )
        
        assert question.question_id == "q1"
        assert question.question_type == QuestionType.MULTIPLE_CHOICE
        assert question.check_answer("4") is True
        assert question.check_answer("3") is False
    
    def test_assessment_creation(self):
        """Test assessment creation and scoring."""
        questions = [
            Question(
                question_id="q1",
                topic_id="arithmetic",
                question_type=QuestionType.NUMERICAL,
                difficulty="easy",
                question_text="What is 5 + 3?",
                correct_answer="8",
                explanation="5 + 3 = 8",
                points=1
            ),
            Question(
                question_id="q2",
                topic_id="arithmetic",
                question_type=QuestionType.NUMERICAL,
                difficulty="medium",
                question_text="What is 12 × 7?",
                correct_answer="84",
                explanation="12 × 7 = 84",
                points=2
            )
        ]
        
        assessment = Assessment(
            assessment_id="test_assessment",
            topic_id="arithmetic",
            name="Arithmetic Test",
            description="Basic arithmetic assessment",
            questions=questions,
            passing_score=0.7
        )
        
        assert len(assessment.questions) == 2
        
        # Test scoring
        answers = {"q1": "8", "q2": "84"}
        score = assessment.calculate_score(answers)
        assert score == 1.0  # Perfect score
        
        partial_answers = {"q1": "8", "q2": "80"}
        partial_score = assessment.calculate_score(partial_answers)
        assert partial_score == 1/3  # Only q1 correct (1 point out of 3 total)
    
    def test_knowledge_rating(self):
        """Test knowledge rating functionality."""
        rating = KnowledgeRating(
            student_id="test_student",
            topic_id="fractions",
            knowledge_level=KnowledgeLevel.BASIC,
            confidence=0.8,
            evidence=["Answered 2/3 questions correctly"],
            needs_learning=True
        )
        
        assert rating.knowledge_level == KnowledgeLevel.BASIC
        assert rating.should_teach_topic() is True
        assert rating.can_advance() is False
        
        # Test excellent rating
        excellent_rating = KnowledgeRating(
            student_id="test_student",
            topic_id="algebra",
            knowledge_level=KnowledgeLevel.EXCELLENT,
            confidence=0.95,
            evidence=["Perfect score on assessment"],
            needs_learning=False
        )
        
        assert excellent_rating.should_teach_topic() is False
        assert excellent_rating.can_advance() is True


class TestSessionModels:
    """Test cases for session-related models."""
    
    def test_session_state_creation(self):
        """Test session state creation."""
        state = SessionState(
            session_id="test_session",
            student_id="test_student",
            current_phase=SessionPhase.INITIALIZATION
        )
        
        assert state.session_id == "test_session"
        assert state.current_phase == SessionPhase.INITIALIZATION
        assert state.tutor_agent_state == AgentState.IDLE
    
    def test_session_state_management(self):
        """Test session state management."""
        state = SessionState(
            session_id="test_session",
            student_id="test_student"
        )
        
        # Test agent state management
        state.set_agent_state("tutor", AgentState.ACTIVE)
        assert state.get_agent_state("tutor") == AgentState.ACTIVE
        
        state.set_agent_state("testing", AgentState.COMPLETED)
        assert state.get_agent_state("testing") == AgentState.COMPLETED
        
        # Test message management
        state.add_agent_message("tutor", "testing", "Start assessment")
        messages = state.agent_messages.get("tutor_to_testing", [])
        assert len(messages) == 1
        assert messages[0] == "Start assessment"
    
    def test_tutoring_session(self):
        """Test tutoring session functionality."""
        state = SessionState(
            session_id="session_001",
            student_id="student_001"
        )
        
        session = TutoringSession(
            session_id="session_001",
            student_id="student_001",
            state=state,
            max_duration_minutes=60,
            target_topics=["fractions", "algebra"]
        )
        
        assert session.is_active is True
        assert session.duration_minutes >= 0
        assert len(session.target_topics) == 2
        
        # Test session completion
        session.end_session()
        assert session.is_active is False
        assert session.ended_at is not None
        assert session.state.current_phase == SessionPhase.COMPLETED
