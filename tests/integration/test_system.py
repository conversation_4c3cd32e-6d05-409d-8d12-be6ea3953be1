"""
Integration tests for the complete Mavia Math Tutor system.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path

from src.mavia_tutor.main import MaviaTutor
from src.mavia_tutor.models.student import MathLevel, LearningStyle


@pytest.fixture
def temp_data_dir():
    """Create a temporary directory for test data."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def tutor_system(temp_data_dir):
    """Create a Mavia Tutor system for testing."""
    return MaviaTutor(
        curriculum_path=str(Path(temp_data_dir) / "curriculum"),
        student_data_path=str(Path(temp_data_dir) / "students"),
        session_data_path=str(Path(temp_data_dir) / "sessions")
    )


class TestSystemIntegration:
    """Integration tests for the complete system."""
    
    def test_system_initialization(self, tutor_system):
        """Test that the system initializes correctly."""
        assert tutor_system.curriculum is not None
        assert tutor_system.tutor_agent is not None
        assert tutor_system.student_manager is not None
        assert tutor_system.session_manager is not None
        
        # Check curriculum has default topics
        assert len(tutor_system.curriculum.topics) > 0
        assert len(tutor_system.curriculum.learning_paths) > 0
    
    def test_student_lifecycle(self, tutor_system):
        """Test complete student lifecycle."""
        # Create student
        student = tutor_system.create_student(
            student_id="integration_test_001",
            name="Integration Test Student",
            age=13,
            grade_level="8th",
            math_level="elementary",
            learning_style="visual",
            goals=["Master fractions", "Learn basic algebra"],
            strengths=["Visual learning", "Pattern recognition"],
            challenges=["Word problems", "Time management"]
        )
        
        assert student.profile.student_id == "integration_test_001"
        assert student.profile.name == "Integration Test Student"
        assert student.profile.math_level == MathLevel.ELEMENTARY
        assert student.profile.learning_style == LearningStyle.VISUAL
        
        # Retrieve student
        retrieved_student = tutor_system.get_student("integration_test_001")
        assert retrieved_student is not None
        assert retrieved_student.profile.name == "Integration Test Student"
        
        # Update student progress
        success = tutor_system.student_manager.update_topic_progress(
            student_id="integration_test_001",
            topic_id="fractions_intro",
            topic_name="Introduction to Fractions",
            correct=True,
            time_spent=25.5
        )
        assert success is True
        
        # Check mastery level
        mastery = tutor_system.student_manager.get_mastery_level(
            "integration_test_001", 
            "fractions_intro"
        )
        assert mastery > 0.0
        
        # Get student summary
        summary = tutor_system.get_student_summary("integration_test_001")
        assert summary["student_id"] == "integration_test_001"
        assert summary["name"] == "Integration Test Student"
    
    @pytest.mark.asyncio
    async def test_tutoring_session_flow(self, tutor_system):
        """Test a complete tutoring session flow."""
        # Create a student first
        student = tutor_system.create_student(
            student_id="session_test_001",
            name="Session Test Student",
            math_level="beginner",
            learning_style="kinesthetic",
            goals=["Learn basic arithmetic"]
        )
        
        # Start a tutoring session
        session_id = await tutor_system.start_tutoring_session(
            student_id="session_test_001",
            target_topics=["basic_arithmetic"],
            max_duration_minutes=30,
            session_goals=["Practice addition and subtraction"]
        )
        
        assert session_id is not None
        assert session_id.startswith("session_")
        
        # Get session summary
        summary = tutor_system.get_session_summary(session_id)
        assert summary["session_id"] == session_id
        assert summary["student_id"] == "session_test_001"
        assert "duration_minutes" in summary
    
    def test_curriculum_management(self, tutor_system):
        """Test curriculum loading and management."""
        curriculum = tutor_system.curriculum
        
        # Test topic retrieval
        basic_arithmetic = curriculum.get_topic("basic_arithmetic")
        assert basic_arithmetic is not None
        assert basic_arithmetic.name == "Basic Arithmetic"
        
        # Test learning path retrieval
        beginner_path = curriculum.get_learning_path("beginner_math")
        assert beginner_path is not None
        assert beginner_path.name == "Beginner Math Fundamentals"
        
        # Test available topics
        available_topics = curriculum.get_available_topics(set())
        assert len(available_topics) > 0
        
        # Test with mastered topics
        mastered = {"basic_arithmetic"}
        available_after = curriculum.get_available_topics(mastered)
        assert len(available_after) >= len(available_topics)
    
    def test_multiple_students(self, tutor_system):
        """Test managing multiple students."""
        # Create multiple students
        students_data = [
            {
                "student_id": "multi_test_001",
                "name": "Alice Johnson",
                "math_level": "beginner",
                "learning_style": "visual"
            },
            {
                "student_id": "multi_test_002", 
                "name": "Bob Smith",
                "math_level": "elementary",
                "learning_style": "auditory"
            },
            {
                "student_id": "multi_test_003",
                "name": "Carol Davis",
                "math_level": "intermediate",
                "learning_style": "kinesthetic"
            }
        ]
        
        created_students = []
        for data in students_data:
            student = tutor_system.create_student(**data)
            created_students.append(student)
        
        # Verify all students were created
        assert len(created_students) == 3
        
        # List all students
        all_students = tutor_system.list_students()
        student_ids = {s["student_id"] for s in all_students}
        
        for data in students_data:
            assert data["student_id"] in student_ids
        
        # Test individual retrieval
        for data in students_data:
            student = tutor_system.get_student(data["student_id"])
            assert student is not None
            assert student.profile.name == data["name"]
    
    @pytest.mark.asyncio
    async def test_session_state_management(self, tutor_system):
        """Test session state management throughout a session."""
        # Create student
        student = tutor_system.create_student(
            student_id="state_test_001",
            name="State Test Student",
            math_level="elementary"
        )
        
        # Create session manually to test state management
        session = tutor_system.session_manager.create_session(
            student=student,
            target_topics=["fractions_intro"],
            max_duration_minutes=45
        )
        
        session_id = session.session_id
        
        # Test session state updates
        success = tutor_system.session_manager.update_session_state(
            session_id, 
            {"current_topic_id": "fractions_intro"}
        )
        assert success is True
        
        # Test agent state management
        success = tutor_system.session_manager.set_agent_state(
            session_id, 
            "tutor", 
            "active"
        )
        assert success is True
        
        agent_state = tutor_system.session_manager.get_agent_state(session_id, "tutor")
        assert agent_state.value == "active"
        
        # Test agent messages
        success = tutor_system.session_manager.add_agent_message(
            session_id,
            "tutor",
            "testing", 
            "Start assessment for fractions"
        )
        assert success is True
        
        messages = tutor_system.session_manager.get_agent_messages(
            session_id,
            "tutor",
            "testing"
        )
        assert len(messages) == 1
        assert messages[0] == "Start assessment for fractions"
        
        # Test topic completion
        success = tutor_system.session_manager.complete_topic(session_id, "fractions_intro")
        assert success is True
        
        # Test session completion
        success = tutor_system.session_manager.end_session(session_id)
        assert success is True
        
        # Verify session is no longer active
        ended_session = tutor_system.session_manager.get_session(session_id)
        assert ended_session is None  # Should be removed from active sessions
    
    def test_error_handling(self, tutor_system):
        """Test system error handling."""
        # Test getting non-existent student
        student = tutor_system.get_student("nonexistent_student")
        assert student is None
        
        # Test invalid session operations
        success = tutor_system.session_manager.update_session_state(
            "nonexistent_session", 
            {"test": "value"}
        )
        assert success is False
        
        # Test invalid agent state operations
        success = tutor_system.session_manager.set_agent_state(
            "nonexistent_session",
            "tutor",
            "active"
        )
        assert success is False
        
        agent_state = tutor_system.session_manager.get_agent_state(
            "nonexistent_session", 
            "tutor"
        )
        assert agent_state is None
    
    @pytest.mark.asyncio
    async def test_concurrent_sessions(self, tutor_system):
        """Test handling multiple concurrent sessions."""
        # Create multiple students
        students = []
        for i in range(3):
            student = tutor_system.create_student(
                student_id=f"concurrent_test_{i:03d}",
                name=f"Concurrent Student {i+1}",
                math_level="beginner"
            )
            students.append(student)
        
        # Start multiple sessions concurrently
        session_tasks = []
        for student in students:
            task = tutor_system.start_tutoring_session(
                student_id=student.profile.student_id,
                max_duration_minutes=15  # Shorter for testing
            )
            session_tasks.append(task)
        
        # Wait for all sessions to complete
        session_ids = await asyncio.gather(*session_tasks, return_exceptions=True)
        
        # Verify all sessions completed successfully
        successful_sessions = [sid for sid in session_ids if isinstance(sid, str)]
        assert len(successful_sessions) == 3
        
        # Verify each session has a summary
        for session_id in successful_sessions:
            summary = tutor_system.get_session_summary(session_id)
            assert summary is not None or summary == {}  # Empty dict if session not found


@pytest.mark.asyncio
async def test_end_to_end_workflow(tutor_system):
    """Test complete end-to-end workflow."""
    # 1. Create a student
    student = tutor_system.create_student(
        student_id="e2e_test_001",
        name="End-to-End Test Student",
        age=14,
        grade_level="9th",
        math_level="intermediate",
        learning_style="reading_writing",
        goals=["Master algebra basics", "Improve problem solving"],
        strengths=["Logical thinking", "Reading comprehension"],
        challenges=["Visual-spatial tasks", "Time pressure"]
    )
    
    # 2. Verify student creation
    assert student.profile.student_id == "e2e_test_001"
    
    # 3. Start tutoring session
    session_id = await tutor_system.start_tutoring_session(
        student_id="e2e_test_001",
        target_topics=["algebra_basics"],
        session_goals=["Learn variables and expressions"],
        max_duration_minutes=40
    )
    
    # 4. Verify session completion
    assert session_id is not None
    
    # 5. Check final state
    student_summary = tutor_system.get_student_summary("e2e_test_001")
    session_summary = tutor_system.get_session_summary(session_id)
    
    assert student_summary["student_id"] == "e2e_test_001"
    assert session_summary is not None or session_summary == {}
    
    print("✅ End-to-end workflow completed successfully!")
