"""
Pytest configuration and shared fixtures for Mavia Math Tutor tests.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
import sys

# Add src to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.mavia_tutor.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, StudentManager, SessionManager
from src.mavia_tutor.models.student import Student, StudentProfile, LearningProgress, MathLevel, LearningStyle
from src.mavia_tutor.models.curriculum import Curriculum


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test data."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def curriculum_loader(temp_directory):
    """Create a curriculum loader with temporary directory."""
    return CurriculumLoader(str(temp_directory / "curriculum"))


@pytest.fixture
def student_manager(temp_directory):
    """Create a student manager with temporary directory."""
    return StudentManager(str(temp_directory / "students"))


@pytest.fixture
def session_manager(temp_directory):
    """Create a session manager with temporary directory."""
    return SessionManager(str(temp_directory / "sessions"))


@pytest.fixture
def sample_curriculum(curriculum_loader):
    """Create a sample curriculum for testing."""
    return curriculum_loader.load_default_curriculum()


@pytest.fixture
def sample_student_profile():
    """Create a sample student profile for testing."""
    return StudentProfile(
        student_id="test_student_001",
        name="Test Student",
        age=13,
        grade_level="8th",
        math_level=MathLevel.ELEMENTARY,
        learning_style=LearningStyle.VISUAL,
        goals=["Learn fractions", "Improve algebra"],
        strengths=["Visual learning", "Pattern recognition"],
        challenges=["Word problems", "Time management"]
    )


@pytest.fixture
def sample_student(sample_student_profile):
    """Create a complete sample student for testing."""
    progress = LearningProgress(student_id=sample_student_profile.student_id)
    return Student(profile=sample_student_profile, progress=progress)


@pytest.fixture
def sample_session_data():
    """Create sample session data for testing."""
    return {
        "student_id": "test_student_001",
        "student_profile": {
            "student_id": "test_student_001",
            "name": "Test Student",
            "math_level": "elementary",
            "learning_style": "visual",
            "goals": ["Learn fractions", "Improve algebra"],
            "strengths": ["Visual learning"],
            "challenges": ["Word problems"]
        },
        "student_progress": {
            "topics_progress": {
                "basic_arithmetic": {
                    "mastery_level": 0.8,
                    "attempts": 5,
                    "correct_answers": 4
                }
            }
        },
        "session_goals": ["Practice fractions", "Review arithmetic"],
        "current_topic_id": None,
        "current_learning_path_id": None
    }


@pytest.fixture
def mock_assessment_data():
    """Create mock assessment data for testing."""
    return {
        "assessment_id": "test_assessment_001",
        "topic_id": "basic_arithmetic",
        "name": "Basic Arithmetic Assessment",
        "description": "Test basic arithmetic skills",
        "questions": [
            {
                "question_id": "q1",
                "topic_id": "basic_arithmetic",
                "question_type": "multiple_choice",
                "difficulty": "easy",
                "question_text": "What is 15 + 27?",
                "options": ["32", "42", "52", "62"],
                "correct_answer": "42",
                "explanation": "15 + 27 = 42",
                "points": 1
            },
            {
                "question_id": "q2",
                "topic_id": "basic_arithmetic",
                "question_type": "numerical",
                "difficulty": "medium",
                "question_text": "Calculate: 8 × 7",
                "correct_answer": "56",
                "explanation": "8 × 7 = 56",
                "points": 2
            }
        ],
        "time_limit_minutes": 15,
        "passing_score": 0.7
    }


@pytest.fixture
def mock_student_responses():
    """Create mock student responses for testing."""
    return {
        "q1": "42",  # Correct
        "q2": "54"   # Incorrect (should be 56)
    }


@pytest.fixture
def mock_learning_path():
    """Create a mock learning path for testing."""
    return {
        "path_id": "test_path_001",
        "name": "Test Learning Path",
        "description": "A test learning path for unit testing",
        "target_level": "elementary",
        "topic_sequence": ["basic_arithmetic", "fractions_intro", "decimals_basic"],
        "estimated_duration_hours": 2.5,
        "learning_objectives": [
            "Master basic arithmetic operations",
            "Understand fraction concepts",
            "Work with decimal numbers"
        ],
        "adaptation_notes": [
            "Use visual aids for better understanding",
            "Provide extra practice for difficult concepts"
        ],
        "created_for_student": "test_student_001",
        "created_at": "2024-01-01T10:00:00"
    }


@pytest.fixture
def mock_topic_explanation():
    """Create a mock topic explanation for testing."""
    return {
        "topic_id": "fractions_intro",
        "topic_name": "Introduction to Fractions",
        "student_id": "test_student_001",
        "learning_style_adapted": "visual",
        "explanation_content": {
            "introduction": "Fractions represent parts of a whole...",
            "prerequisites": ["Basic arithmetic", "Understanding of whole numbers"],
            "core_concept": "A fraction has two parts: numerator and denominator...",
            "key_points": [
                "Numerator shows how many parts we have",
                "Denominator shows total parts in the whole"
            ],
            "common_mistakes": [
                "Confusing numerator and denominator",
                "Not simplifying fractions"
            ]
        },
        "practice_problems": [
            {
                "problem": "What fraction represents 3 out of 4 equal parts?",
                "solution": "3/4",
                "steps": ["Count the parts we have: 3", "Count total parts: 4", "Write as fraction: 3/4"],
                "difficulty": "easy"
            }
        ],
        "teaching_notes": [
            "Use visual representations",
            "Check understanding frequently"
        ],
        "next_steps": [
            "Practice identifying fractions",
            "Work with equivalent fractions"
        ],
        "created_at": "2024-01-01T10:30:00"
    }


# Test utilities
def assert_valid_uuid(uuid_string):
    """Assert that a string is a valid UUID format."""
    import re
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$',
        re.IGNORECASE
    )
    assert uuid_pattern.match(uuid_string), f"Invalid UUID format: {uuid_string}"


def assert_valid_timestamp(timestamp_string):
    """Assert that a string is a valid ISO timestamp."""
    from datetime import datetime
    try:
        datetime.fromisoformat(timestamp_string.replace('Z', '+00:00'))
    except ValueError:
        pytest.fail(f"Invalid timestamp format: {timestamp_string}")


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically."""
    for item in items:
        # Add 'unit' marker to tests in test_unit directory
        if "test_unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        
        # Add 'integration' marker to tests in test_integration directory
        elif "test_integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add 'slow' marker to tests that might take longer
        if "test_system" in item.name or "test_end_to_end" in item.name:
            item.add_marker(pytest.mark.slow)
