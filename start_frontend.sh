#!/bin/bash

# Mavia Math Tutor - Frontend Startup Script

echo "🎨 Starting Mavia Math Tutor Frontend..."

# Navigate to frontend directory
cd frontend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your configuration"
    else
        echo "❌ .env.example not found. Please create .env file manually."
        exit 1
    fi
fi

# Start the development server
echo "🌐 Starting Svelte development server on http://localhost:5173..."
echo "🔗 Backend API: http://localhost:8000"
echo ""
echo "Make sure the backend is running on port 8000"
echo "Press Ctrl+C to stop the server"
echo ""

# Run the development server
npm run dev
