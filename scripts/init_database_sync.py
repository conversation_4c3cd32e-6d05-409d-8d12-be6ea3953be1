#!/usr/bin/env python3
"""Initialize the database with tables and sample data using synchronous operations."""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

# Import database components
sys.path.insert(0, str(project_root / "src"))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from mavia_tutor.database.base import Base

# Import models to register them
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress


def get_sync_database_url() -> str:
    """Get synchronous database URL from environment variables."""
    # Convert async URL to sync URL
    database_url = os.getenv("DATABASE_URL")
    if database_url and database_url.startswith("postgresql+asyncpg://"):
        # Replace asyncpg with psycopg2 for synchronous operations
        return database_url.replace("postgresql+asyncpg://", "postgresql://")
    elif database_url:
        return database_url
    
    # Fallback to individual components
    host = os.getenv("DB_HOST", "localhost")
    port = os.getenv("DB_PORT", "5432")
    name = os.getenv("DB_NAME", "mavia")
    user = os.getenv("DB_USER", "postgres")
    password = os.getenv("DB_PASSWORD", "password")
    
    return f"postgresql://{user}:{password}@{host}:{port}/{name}"


def create_tables():
    """Create all database tables synchronously."""
    database_url = get_sync_database_url()
    engine = create_engine(database_url, echo=True)
    
    print("🗑️ Dropping existing tables...")
    Base.metadata.drop_all(engine)
    
    print("🏗️ Creating new tables...")
    Base.metadata.create_all(engine)
    
    # Verify tables were created
    with engine.connect() as conn:
        result = conn.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"))
        tables = [row[0] for row in result.fetchall()]
        
        print(f"\n📋 Created tables ({len(tables)}):")
        for table in sorted(tables):
            print(f"   - {table}")
    
    engine.dispose()
    print("✅ Database tables created successfully!")
    return database_url


def create_sample_data(database_url: str):
    """Create sample data synchronously."""
    engine = create_engine(database_url, echo=False)
    Session = sessionmaker(bind=engine)
    
    with Session() as session:
        # Create sample user
        user = User(
            email="<EMAIL>",
            role="student",
            first_name="Test",
            last_name="Student",
            subscription_status="active"
        )
        session.add(user)
        session.flush()  # Get the user ID
        
        # Create sample student profile
        student = StudentProfile(
            user_id=user.id,
            student_id="test_student_001",
            name="Test Student",
            math_level="intermediate",
            age=15,
            grade_level="9th",
            goals=["Improve algebra skills", "Prepare for SAT"],
            strengths=["Problem solving", "Logical thinking"],
            challenges=["Word problems", "Geometry"]
        )
        session.add(student)
        session.flush()  # Get the student ID
        
        # Create sample curriculum
        curriculum = Curriculum(
            curriculum_id="math_basics_v1",
            name="Math Basics",
            description="Fundamental mathematics curriculum",
            version="1.0"
        )
        session.add(curriculum)
        session.flush()  # Get the curriculum ID
        
        # Create sample topic
        topic = Topic(
            curriculum_id=curriculum.id,
            topic_id="algebra_basics",
            name="Algebra Basics",
            description="Introduction to algebraic concepts",
            category="algebra",
            difficulty="medium",
            prerequisites=[],
            learning_objectives=["Understand variables", "Solve linear equations"],
            key_concepts=["Variables", "Equations", "Substitution"],
            examples=["x + 5 = 10", "2y - 3 = 7"],
            estimated_time_minutes=45
        )
        session.add(topic)
        
        # Commit all changes
        session.commit()
        
        print("✅ Sample data created successfully!")
        print(f"   User: {user.email} (ID: {user.id})")
        print(f"   Student: {student.name} (ID: {student.student_id})")
        print(f"   Curriculum: {curriculum.name} (ID: {curriculum.curriculum_id})")
        print(f"   Topic: {topic.name} (ID: {topic.topic_id})")
    
    engine.dispose()


def verify_data(database_url: str):
    """Verify the data was created correctly."""
    engine = create_engine(database_url, echo=False)
    
    with engine.connect() as conn:
        # Check users
        result = conn.execute(text("SELECT COUNT(*) FROM users"))
        user_count = result.scalar()
        
        # Check students
        result = conn.execute(text("SELECT COUNT(*) FROM student_profiles"))
        student_count = result.scalar()
        
        # Check curricula
        result = conn.execute(text("SELECT COUNT(*) FROM curricula"))
        curriculum_count = result.scalar()
        
        # Check topics
        result = conn.execute(text("SELECT COUNT(*) FROM topics"))
        topic_count = result.scalar()
        
        print(f"\n📊 Data verification:")
        print(f"   Users: {user_count}")
        print(f"   Students: {student_count}")
        print(f"   Curricula: {curriculum_count}")
        print(f"   Topics: {topic_count}")
    
    engine.dispose()


def main():
    """Main initialization function."""
    print("🚀 Initializing Mavia Math Tutor Database (Synchronous Mode)...")
    
    # Check if database URL is configured
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable not set!")
        print("   Please set up your .env file with database configuration.")
        return
    
    sync_url = get_sync_database_url()
    print(f"🔗 Database URL: {sync_url}")
    
    try:
        # Create tables
        database_url = create_tables()
        
        # Create sample data
        create_sample_data(database_url)
        
        # Verify data
        verify_data(database_url)
        
        print("\n🎉 Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Test the API endpoints:")
        print("   curl http://localhost:8000/api/db/students/test_student_001")
        print("2. Start the backend server: python main.py")
        print("3. Start the frontend: cd frontend && npm run dev")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
