#!/usr/bin/env python3
"""
Complete database setup script for Mavia Math Tutor.

This script:
1. Creates all database tables
2. Runs migrations to populate curriculum data
3. Creates sample user and student data
4. Initializes progress tracking tables

Usage:
    python scripts/setup_database_complete.py
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession
from mavia_tutor.database.config import get_async_engine
from mavia_tutor.database.base import Base

# Import all models to register them
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress

# Import services
from mavia_tutor.database.services.user_service import UserService
from mavia_tutor.database.services.student_service import StudentService
from mavia_tutor.database.services.curriculum_service import CurriculumService
from mavia_tutor.database.services.progress_service import ProgressService


async def create_tables():
    """Create all database tables."""
    print("🔧 Creating database tables...")
    
    engine = get_async_engine()
    
    async with engine.begin() as conn:
        # Drop all tables first (for clean setup)
        await conn.run_sync(Base.metadata.drop_all)
        print("   ✅ Dropped existing tables")
        
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)
        print("   ✅ Created all tables")
    
    await engine.dispose()


async def run_migrations():
    """Run Alembic migrations to populate curriculum data."""
    print("📚 Running migrations to populate curriculum data...")
    
    import subprocess
    import os
    
    # Change to project root directory
    original_cwd = os.getcwd()
    os.chdir(project_root)
    
    try:
        # Run alembic upgrade
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True,
            check=True
        )
        print("   ✅ Migrations completed successfully")
        if result.stdout:
            print(f"   📝 Migration output: {result.stdout}")
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Migration failed: {e}")
        print(f"   📝 Error output: {e.stderr}")
        raise
    finally:
        os.chdir(original_cwd)


async def create_sample_data():
    """Create sample user and student data."""
    print("👤 Creating sample user and student data...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        # Create services
        user_service = UserService(session)
        student_service = StudentService(session)
        progress_service = ProgressService(session)
        
        # Create sample user
        user = await user_service.create_or_update_user(
            user_id="user_sample_123",
            email="<EMAIL>",
            first_name="Sample",
            last_name="User"
        )
        
        # Create sample student profile
        student = await student_service.create_student(
            student_id="student_sample_123",
            user_id=user.id,
            name="Sample Student",
            age=12,
            math_level="intermediate",
            learning_style="visual",
            user_email="<EMAIL>"
        )
        
        # Initialize learning progress
        await progress_service.create_or_update_progress(
            student_id=student.id,
            current_path_id=None,
            total_sessions=0,
            total_time_minutes=0,
            achievements=[]
        )
        
        await session.commit()
        
        print(f"   ✅ Created user: {user.email} (ID: {user.id})")
        print(f"   ✅ Created student: {student.name} (ID: {student.student_id})")
    
    await engine.dispose()


async def verify_setup():
    """Verify that the database setup was successful."""
    print("🔍 Verifying database setup...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        curriculum_service = CurriculumService(session)
        student_service = StudentService(session)
        
        # Check curricula
        curricula = await curriculum_service.get_active_curricula()
        print(f"   📚 Found {len(curricula)} active curricula")
        
        for curriculum in curricula:
            topics_count = len(await curriculum_service.get_curriculum_topics(curriculum.id))
            paths_count = len(await curriculum_service.get_curriculum_paths(curriculum.id))
            print(f"      - {curriculum.name}: {topics_count} topics, {paths_count} learning paths")
        
        # Check students
        students = await student_service.get_all_students(limit=10)
        print(f"   👤 Found {len(students.students)} students")
        
        for student in students.students:
            print(f"      - {student.name} ({student.student_id})")
    
    await engine.dispose()


async def main():
    """Main setup function."""
    print("🚀 Starting complete database setup for Mavia Math Tutor")
    print("=" * 60)
    
    try:
        # Step 1: Create tables
        await create_tables()
        
        # Step 2: Run migrations
        await run_migrations()
        
        # Step 3: Create sample data
        await create_sample_data()
        
        # Step 4: Verify setup
        await verify_setup()
        
        print("=" * 60)
        print("✅ Database setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Start the application server")
        print("   2. Create additional users through the web interface")
        print("   3. Begin tutoring sessions to populate progress data")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
