#!/usr/bin/env python3
"""Create database tables using the same approach as the API."""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

# Import database components
sys.path.insert(0, str(project_root / "src"))

from mavia_tutor.database.config import get_async_engine, async_session_factory
from mavia_tutor.database.base import Base

# Import models to register them
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress


async def create_tables():
    """Create all database tables."""
    engine = get_async_engine()
    
    # Create tables using the engine
    async with engine.begin() as conn:
        print("🗑️ Dropping existing tables...")
        await conn.run_sync(Base.metadata.drop_all)
        
        print("🏗️ Creating new tables...")
        await conn.run_sync(Base.metadata.create_all)
    
    print("✅ Database tables created successfully!")
    
    # Test that we can create a session and it sees the tables
    async with async_session_factory() as session:
        # Try a simple query to verify tables exist
        from sqlalchemy import text
        result = await session.execute(text("SELECT COUNT(*) FROM users"))
        count = result.scalar()
        print(f"✅ Users table accessible, current count: {count}")
    
    await engine.dispose()


async def main():
    """Main function."""
    print("🚀 Creating Mavia Math Tutor Database Tables...")
    
    # Check if database URL is configured
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable not set!")
        return
    
    print(f"🔗 Database URL: {database_url}")
    
    try:
        await create_tables()
        print("\n🎉 Table creation completed successfully!")
        print("You can now test the API endpoints!")
        
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
