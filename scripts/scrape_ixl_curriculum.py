#!/usr/bin/env python3
"""
IXL Curriculum Scraper Script

This script scrapes the IXL Maths UK website to extract year-wise, topic-wise
skills and integrates them with the Mavia Math Tutor curriculum system.

Usage:
    python scripts/scrape_ixl_curriculum.py [--output-dir OUTPUT_DIR] [--integrate]

Options:
    --output-dir: Directory to store scraped data (default: ./data/ixl_curriculum)
    --integrate: Also integrate with Mavia curriculum after scraping
    --years: Specific years to scrape (e.g., "1,2,3" or "all")
    --dry-run: Show what would be scraped without actually scraping
"""

import asyncio
import argparse
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.mavia_tutor.scrapers.ixl_scraper import IXLScraper
from src.mavia_tutor.curriculum_integration import CurriculumIntegrator


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("ixl_scraper.log")
        ]
    )


async def scrape_ixl_curriculum(
    output_dir: str,
    years: str = "all",
    dry_run: bool = False
):
    """
    Scrape IXL curriculum data.
    
    Args:
        output_dir: Directory to store scraped data
        years: Years to scrape ("all" or comma-separated list)
        dry_run: If True, show what would be scraped without scraping
    """
    logger = logging.getLogger(__name__)
    
    # Initialize scraper
    scraper = IXLScraper(output_dir)
    
    if dry_run:
        logger.info("DRY RUN MODE - No actual scraping will be performed")
        logger.info(f"Would scrape IXL curriculum to: {output_dir}")
        logger.info(f"Target years: {years}")
        
        # Show available year groups
        logger.info("Available year groups:")
        for year_key, year_info in scraper.year_groups.items():
            logger.info(f"  {year_key}: {year_info}")
        
        return None
    
    try:
        logger.info("Starting IXL curriculum scraping...")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Target years: {years}")
        
        # Scrape data
        if years == "all":
            scraped_data = await scraper.scrape_all_years()
        else:
            # TODO: Implement selective year scraping
            logger.warning("Selective year scraping not yet implemented, scraping all years")
            scraped_data = await scraper.scrape_all_years()
        
        # Convert to Mavia format
        logger.info("Converting scraped data to Mavia curriculum format...")
        curriculum_data = scraper.convert_to_mavia_curriculum(scraped_data)
        
        logger.info("IXL curriculum scraping completed successfully!")
        logger.info(f"Scraped {len(curriculum_data['topics'])} topics")
        logger.info(f"Created {len(curriculum_data['learning_paths'])} learning paths")
        
        return curriculum_data
        
    except Exception as e:
        logger.error(f"Error during scraping: {e}")
        raise


async def integrate_curriculum(ixl_data_path: str, output_dir: str):
    """
    Integrate IXL curriculum with Mavia's default curriculum.
    
    Args:
        ixl_data_path: Path to IXL curriculum JSON file
        output_dir: Directory to store integrated curriculum
    """
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting curriculum integration...")
        
        # Initialize integrator
        integrator = CurriculumIntegrator(output_dir)
        
        # Perform integration
        integrated_curriculum = integrator.integrate_ixl_curriculum(ixl_data_path)
        
        logger.info("Curriculum integration completed successfully!")
        logger.info(f"Integrated curriculum has {len(integrated_curriculum.topics)} topics")
        logger.info(f"Integrated curriculum has {len(integrated_curriculum.learning_paths)} learning paths")
        
        return integrated_curriculum
        
    except Exception as e:
        logger.error(f"Error during integration: {e}")
        raise


def show_scraping_summary(output_dir: str):
    """Show summary of scraped data."""
    logger = logging.getLogger(__name__)
    
    output_path = Path(output_dir)
    
    # Check for scraped files
    json_files = list(output_path.glob("*.json"))
    
    if not json_files:
        logger.warning(f"No scraped data found in {output_dir}")
        return
    
    logger.info(f"\n📊 Scraping Summary:")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Files created: {len(json_files)}")
    
    for file_path in sorted(json_files):
        file_size = file_path.stat().st_size
        logger.info(f"  📄 {file_path.name} ({file_size:,} bytes)")
    
    # Show curriculum statistics if available
    curriculum_file = output_path / "mavia_ixl_curriculum.json"
    if curriculum_file.exists():
        import json
        try:
            with open(curriculum_file, 'r', encoding='utf-8') as f:
                curriculum_data = json.load(f)
            
            logger.info(f"\n📚 Curriculum Statistics:")
            logger.info(f"Total topics: {curriculum_data.get('total_topics', 0)}")
            logger.info(f"Total learning paths: {curriculum_data.get('total_paths', 0)}")
            logger.info(f"Source: {curriculum_data.get('source', 'Unknown')}")
            logger.info(f"Scraped at: {curriculum_data.get('scraped_at', 'Unknown')}")
            
        except Exception as e:
            logger.warning(f"Could not read curriculum statistics: {e}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Scrape IXL Maths curriculum and integrate with Mavia Math Tutor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Scrape all years to default directory
  python scripts/scrape_ixl_curriculum.py

  # Scrape to specific directory and integrate
  python scripts/scrape_ixl_curriculum.py --output-dir ./my_curriculum --integrate

  # Dry run to see what would be scraped
  python scripts/scrape_ixl_curriculum.py --dry-run

  # Scrape specific years (not yet implemented)
  python scripts/scrape_ixl_curriculum.py --years "1,2,3,4,5"
        """
    )
    
    parser.add_argument(
        "--output-dir",
        default="./data/ixl_curriculum",
        help="Directory to store scraped data (default: ./data/ixl_curriculum)"
    )
    
    parser.add_argument(
        "--integrate",
        action="store_true",
        help="Also integrate with Mavia curriculum after scraping"
    )
    
    parser.add_argument(
        "--years",
        default="all",
        help="Years to scrape: 'all' or comma-separated list (e.g., '1,2,3')"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be scraped without actually scraping"
    )
    
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--integration-output",
        default="./data/integrated_curriculum",
        help="Directory for integrated curriculum output"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🎓 IXL Curriculum Scraper for Mavia Math Tutor")
        logger.info("=" * 60)
        
        # Create output directories
        Path(args.output_dir).mkdir(parents=True, exist_ok=True)
        if args.integrate:
            Path(args.integration_output).mkdir(parents=True, exist_ok=True)
        
        # Phase 1: Scrape IXL curriculum
        logger.info("Phase 1: Scraping IXL curriculum data...")
        curriculum_data = await scrape_ixl_curriculum(
            output_dir=args.output_dir,
            years=args.years,
            dry_run=args.dry_run
        )
        
        if args.dry_run:
            logger.info("Dry run completed. No data was scraped.")
            return
        
        # Show scraping summary
        show_scraping_summary(args.output_dir)
        
        # Phase 2: Integration (if requested)
        if args.integrate and curriculum_data:
            logger.info("\nPhase 2: Integrating with Mavia curriculum...")
            
            ixl_curriculum_file = Path(args.output_dir) / "mavia_ixl_curriculum.json"
            
            if ixl_curriculum_file.exists():
                integrated_curriculum = await integrate_curriculum(
                    str(ixl_curriculum_file),
                    args.integration_output
                )
                
                logger.info(f"\n✅ Integration completed!")
                logger.info(f"Integrated curriculum saved to: {args.integration_output}")
            else:
                logger.error("IXL curriculum file not found for integration")
        
        logger.info("\n🎉 All operations completed successfully!")
        
        if not args.integrate:
            logger.info(f"\n💡 To integrate with Mavia curriculum, run:")
            logger.info(f"python scripts/scrape_ixl_curriculum.py --integrate --output-dir {args.output_dir}")
        
    except KeyboardInterrupt:
        logger.info("\n👋 Operation cancelled by user")
    except Exception as e:
        logger.error(f"\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
