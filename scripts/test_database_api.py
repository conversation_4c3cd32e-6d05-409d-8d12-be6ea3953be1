#!/usr/bin/env python3
"""
Comprehensive test script for the Mavia Math Tutor Database API
Tests all endpoints and functionality
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/db"

class APITester:
    def __init__(self):
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_endpoint(self, method: str, endpoint: str, data: Dict[Any, Any] = None, expected_status: int = 200) -> Dict[Any, Any]:
        """Test a single API endpoint"""
        url = f"{BASE_URL}{endpoint}"
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    result = await response.json()
                    status = response.status
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    result = await response.json()
                    status = response.status
            elif method.upper() == "PUT":
                async with self.session.put(url, json=data) as response:
                    result = await response.json()
                    status = response.status
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            success = status == expected_status
            self.test_results.append({
                "endpoint": endpoint,
                "method": method,
                "status": status,
                "expected_status": expected_status,
                "success": success,
                "response": result if success else None,
                "error": result if not success else None
            })
            
            print(f"{'✅' if success else '❌'} {method} {endpoint} - Status: {status}")
            return result if success else None
            
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {str(e)}")
            self.test_results.append({
                "endpoint": endpoint,
                "method": method,
                "success": False,
                "error": str(e)
            })
            return None
    
    async def run_comprehensive_tests(self):
        """Run all API tests"""
        print("🚀 Starting Comprehensive Database API Tests")
        print("=" * 50)
        
        # Test 1: Initialize Database
        print("\n📊 Testing Database Initialization")
        await self.test_endpoint("POST", "/init")
        
        # Test 2: Create Students
        print("\n👥 Testing Student Management")
        student_data = {
            "student_id": "test_api_001",
            "name": "API Test Student",
            "age": 16,
            "grade_level": "10th",
            "math_level": "intermediate",
            "learning_style": "visual",
            "goals": ["Master algebra", "Improve problem solving"],
            "strengths": ["Logical thinking", "Pattern recognition"],
            "challenges": ["Word problems", "Time management"]
        }
        
        student_result = await self.test_endpoint("POST", "/students", student_data)
        
        # Test 3: Get Student
        if student_result:
            await self.test_endpoint("GET", f"/students/{student_data['student_id']}")
        
        # Test 4: List Students
        await self.test_endpoint("GET", "/students")
        
        # Test 5: Create Session
        print("\n📚 Testing Session Management")
        session_data = {
            "session_id": "test_session_001",
            "student_id": student_data['student_id'],
            "target_topics": ["algebra", "linear_equations"],
            "session_goals": ["Practice solving linear equations", "Review graphing"],
            "max_duration_minutes": 45
        }
        
        session_result = await self.test_endpoint("POST", "/sessions", session_data)
        
        # Test 6: Get Session
        if session_result:
            await self.test_endpoint("GET", f"/sessions/{session_data['session_id']}")
        
        # Test 7: Get Student Sessions
        await self.test_endpoint("GET", f"/students/{student_data['student_id']}/sessions")
        
        # Test 8: End Session
        if session_result:
            await self.test_endpoint("PUT", f"/sessions/{session_data['session_id']}/end")
        
        # Test 9: Get Student Progress
        print("\n📈 Testing Progress Tracking")
        await self.test_endpoint("GET", f"/students/{student_data['student_id']}/progress")
        
        # Test 10: Curriculum Management
        print("\n📖 Testing Curriculum Management")
        await self.test_endpoint("GET", "/curricula")
        
        # Test 11: Create another student for testing
        student_data_2 = {
            "student_id": "test_api_002",
            "name": "Second Test Student",
            "age": 17,
            "grade_level": "11th",
            "math_level": "advanced",
            "goals": ["Master calculus"],
            "strengths": ["Quick learner"],
            "challenges": ["Complex proofs"]
        }
        
        await self.test_endpoint("POST", "/students", student_data_2)
        
        # Test 12: Create session for second student
        session_data_2 = {
            "session_id": "test_session_002",
            "student_id": student_data_2['student_id'],
            "target_topics": ["calculus", "derivatives"],
            "session_goals": ["Learn derivative rules"]
        }
        
        await self.test_endpoint("POST", "/sessions", session_data_2)
        
        # Test 13: Test error handling (non-existent student)
        print("\n🚫 Testing Error Handling")
        await self.test_endpoint("GET", "/students/non_existent_student", expected_status=404)
        
        # Test 14: Test error handling (invalid session)
        await self.test_endpoint("GET", "/sessions/non_existent_session", expected_status=404)
        
        print("\n" + "=" * 50)
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for test in self.test_results if test['success'])
        failed_tests = total_tests - successful_tests
        
        print(f"📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Successful: {successful_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   Success Rate: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for test in self.test_results:
                if not test['success']:
                    print(f"   - {test['method']} {test['endpoint']}: {test.get('error', 'Unknown error')}")
        
        print(f"\n{'🎉 All tests passed!' if failed_tests == 0 else '⚠️  Some tests failed. Check the backend logs.'}")

async def main():
    """Main test function"""
    print("Mavia Math Tutor - Database API Test Suite")
    print("Make sure the backend server is running on http://localhost:8000")
    print()
    
    try:
        async with APITester() as tester:
            await tester.run_comprehensive_tests()
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
