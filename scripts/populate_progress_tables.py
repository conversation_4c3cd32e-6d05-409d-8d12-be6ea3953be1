#!/usr/bin/env python3
"""
Script to populate empty progress tables based on existing learning sessions.

This script analyzes existing tutoring sessions and populates:
- learning_progress table with student session statistics
- topic_progress table with topic-specific progress data
- Ensures curriculum and topics tables are populated

Usage:
    python scripts/populate_progress_tables.py
"""

import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession
from mavia_tutor.database.config import get_async_engine

# Import services
from mavia_tutor.database.services.student_service import StudentService
from mavia_tutor.database.services.session_service import SessionService
from mavia_tutor.database.services.progress_service import ProgressService, TopicProgressService
from mavia_tutor.database.services.curriculum_service import CurriculumService


async def analyze_existing_sessions():
    """Analyze existing tutoring sessions and populate progress tables."""
    print("🔍 Analyzing existing tutoring sessions...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        # Create services
        student_service = StudentService(session)
        session_service = SessionService(session)
        progress_service = ProgressService(session)
        topic_progress_service = TopicProgressService(session)
        curriculum_service = CurriculumService(session)
        
        # Get all students
        students_data = await student_service.get_all_students(limit=1000)
        students = students_data.students
        
        print(f"   📊 Found {len(students)} students")
        
        # Get all sessions
        all_sessions = []
        for student in students:
            student_sessions = await session_service.get_student_sessions(student.id, limit=1000)
            all_sessions.extend(student_sessions.sessions)
        
        print(f"   📊 Found {len(all_sessions)} total sessions")
        
        if not all_sessions:
            print("   ⚠️  No sessions found. Nothing to populate.")
            return
        
        # Get curriculum data for topic mapping
        curricula = await curriculum_service.get_active_curricula()
        topic_mapping = {}
        
        for curriculum in curricula:
            topics = await curriculum_service.get_curriculum_topics(curriculum.id)
            for topic in topics:
                topic_mapping[topic.topic_id] = topic.name
        
        print(f"   📚 Found {len(topic_mapping)} topics in curriculum")
        
        # Process each student's sessions
        for student in students:
            print(f"\n👤 Processing student: {student.name} ({student.student_id})")
            
            # Get student's sessions
            student_sessions_data = await session_service.get_student_sessions(student.id, limit=1000)
            student_sessions = student_sessions_data.sessions
            
            if not student_sessions:
                print(f"   ⚠️  No sessions found for {student.name}")
                continue
            
            # Calculate overall progress statistics
            total_sessions = len(student_sessions)
            total_time_minutes = sum(s.time_spent_minutes for s in student_sessions)
            
            # Collect topics from all sessions
            topics_encountered = set()
            topic_time_spent = {}
            topic_session_count = {}
            
            for sess in student_sessions:
                # Add target topics
                if sess.target_topics:
                    for topic in sess.target_topics:
                        if topic != 'general_learning':  # Skip generic topics
                            topics_encountered.add(topic)
                            topic_time_spent[topic] = topic_time_spent.get(topic, 0) + sess.time_spent_minutes
                            topic_session_count[topic] = topic_session_count.get(topic, 0) + 1
            
            print(f"   📈 Sessions: {total_sessions}, Time: {total_time_minutes}m, Topics: {len(topics_encountered)}")
            
            # Create or update learning progress
            try:
                await progress_service.create_or_update_progress(
                    student_id=student.id,
                    current_path_id=None,  # Will be set when learning paths are assigned
                    total_sessions=total_sessions,
                    total_time_minutes=Decimal(str(total_time_minutes)),
                    achievements=[]  # Will be populated based on mastery levels
                )
                print(f"   ✅ Updated learning progress")
            except Exception as e:
                print(f"   ❌ Failed to update learning progress: {e}")
            
            # Create topic progress entries
            for topic_id in topics_encountered:
                topic_name = topic_mapping.get(topic_id, topic_id.replace('_', ' ').title())
                time_spent = topic_time_spent.get(topic_id, 0)
                session_count = topic_session_count.get(topic_id, 0)
                
                # Estimate mastery level based on time spent and sessions
                # This is a simple heuristic - in a real system, this would be based on assessments
                mastery_level = min(0.8, (time_spent / 60.0) * 0.2 + (session_count * 0.1))
                
                try:
                    await topic_progress_service.update_topic_progress(
                        student_id=student.id,
                        topic_id=topic_id,
                        topic_name=topic_name,
                        mastery_level=Decimal(str(mastery_level)),
                        correct_answers=int(session_count * 3),  # Estimate based on sessions
                        time_spent_minutes=Decimal(str(time_spent))
                    )
                    print(f"      ✅ Updated progress for topic: {topic_name} (mastery: {mastery_level:.2f})")
                except Exception as e:
                    print(f"      ❌ Failed to update topic progress for {topic_name}: {e}")
        
        await session.commit()
        print("\n✅ Progress tables populated successfully!")
    
    await engine.dispose()


async def verify_population():
    """Verify that the progress tables were populated correctly."""
    print("\n🔍 Verifying progress table population...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        progress_service = ProgressService(session)
        topic_progress_service = TopicProgressService(session)
        student_service = StudentService(session)
        
        # Get all students
        students_data = await student_service.get_all_students(limit=1000)
        students = students_data.students
        
        learning_progress_count = 0
        topic_progress_count = 0
        
        for student in students:
            # Check learning progress
            learning_progress = await progress_service.get_student_progress(student.id)
            if learning_progress:
                learning_progress_count += 1
                print(f"   📊 {student.name}: {learning_progress.total_sessions} sessions, {learning_progress.total_time_minutes}m")
            
            # Check topic progress
            topic_progress_list = await topic_progress_service.get_student_all_topic_progress(student.id)
            topic_progress_count += len(topic_progress_list)
            
            if topic_progress_list:
                print(f"      📚 Topics: {len(topic_progress_list)} with progress")
                for tp in topic_progress_list[:3]:  # Show first 3
                    print(f"         - {tp.topic_name}: {tp.mastery_level:.2f} mastery, {tp.attempts} attempts")
        
        print(f"\n📈 Summary:")
        print(f"   - Learning progress records: {learning_progress_count}")
        print(f"   - Topic progress records: {topic_progress_count}")
    
    await engine.dispose()


async def suggest_learning_paths():
    """Suggest learning paths for students based on their progress."""
    print("\n🎯 Suggesting learning paths based on student progress...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        student_service = StudentService(session)
        progress_service = ProgressService(session)
        topic_progress_service = TopicProgressService(session)
        curriculum_service = CurriculumService(session)
        
        # Get all students
        students_data = await student_service.get_all_students(limit=1000)
        students = students_data.students
        
        # Get available learning paths
        curricula = await curriculum_service.get_active_curricula()
        available_paths = []
        for curriculum in curricula:
            paths = await curriculum_service.get_curriculum_paths(curriculum.id)
            available_paths.extend(paths)
        
        print(f"   📚 Found {len(available_paths)} available learning paths")
        
        for student in students:
            learning_progress = await progress_service.get_student_progress(student.id)
            if not learning_progress:
                continue
            
            topic_progress_list = await topic_progress_service.get_student_all_topic_progress(student.id)
            
            # Calculate average mastery level
            if topic_progress_list:
                avg_mastery = sum(tp.mastery_level for tp in topic_progress_list) / len(topic_progress_list)
                
                # Suggest path based on mastery level and math level
                suggested_path = None
                if avg_mastery < 0.3:
                    # Look for beginner paths
                    suggested_path = next((p for p in available_paths if p.target_level == 'beginner'), None)
                elif avg_mastery < 0.6:
                    # Look for elementary paths
                    suggested_path = next((p for p in available_paths if p.target_level == 'elementary'), None)
                else:
                    # Look for intermediate paths
                    suggested_path = next((p for p in available_paths if p.target_level == 'intermediate'), None)
                
                if suggested_path:
                    print(f"   🎯 {student.name}: Suggest '{suggested_path.name}' (avg mastery: {avg_mastery:.2f})")
                    
                    # Update learning progress with suggested path
                    try:
                        await progress_service.create_or_update_progress(
                            student_id=student.id,
                            current_path_id=suggested_path.path_id,
                            total_sessions=learning_progress.total_sessions,
                            total_time_minutes=learning_progress.total_time_minutes,
                            achievements=learning_progress.achievements
                        )
                    except Exception as e:
                        print(f"      ❌ Failed to update path suggestion: {e}")
        
        await session.commit()
    
    await engine.dispose()


async def main():
    """Main function to populate progress tables."""
    print("🚀 Starting progress table population for Mavia Math Tutor")
    print("=" * 60)
    
    try:
        # Step 1: Analyze sessions and populate progress tables
        await analyze_existing_sessions()
        
        # Step 2: Verify population
        await verify_population()
        
        # Step 3: Suggest learning paths
        await suggest_learning_paths()
        
        print("=" * 60)
        print("✅ Progress table population completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Review the populated progress data")
        print("   2. Adjust mastery level calculations if needed")
        print("   3. Implement real-time progress updates in the multi-agent system")
        
    except Exception as e:
        print(f"❌ Progress table population failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
