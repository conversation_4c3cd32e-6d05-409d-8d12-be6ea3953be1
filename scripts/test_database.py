#!/usr/bin/env python3
"""Test database connectivity and basic operations."""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.mavia_tutor.database.config import get_async_session
from src.mavia_tutor.database.services import UserService, StudentService


async def test_database_connection():
    """Test basic database connectivity."""
    try:
        async with get_async_session() as session:
            print("✅ Database connection successful!")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


async def test_basic_operations():
    """Test basic CRUD operations."""
    try:
        async with get_async_session() as session:
            user_service = UserService(session)
            student_service = StudentService(session)
            
            # Test user creation
            user = await user_service.create_user(
                email="<EMAIL>",
                role="student",
                first_name="Test",
                last_name="User"
            )
            print(f"✅ Created user: {user.email}")
            
            # Test student profile creation
            student = await student_service.create_student_profile(
                user_id=user.id,
                student_id="test_001",
                name="Test Student",
                math_level="beginner"
            )
            print(f"✅ Created student: {student.name}")
            
            # Test retrieval
            retrieved_student = await student_service.get_by_student_id("test_001")
            if retrieved_student:
                print(f"✅ Retrieved student: {retrieved_student.name}")
            
            # Test with user relationship
            student_with_user = await student_service.get_with_user("test_001")
            if student_with_user and student_with_user.user:
                print(f"✅ Retrieved student with user: {student_with_user.user.email}")
            
            return True
            
    except Exception as e:
        print(f"❌ Database operations failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🧪 Testing Mavia Math Tutor Database...")
    
    # Check environment
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable not set!")
        print("   Please set up your .env file with database configuration.")
        return
    
    print(f"🔗 Database URL: {database_url}")
    
    # Test connection
    if not await test_database_connection():
        return
    
    # Test operations
    if await test_basic_operations():
        print("\n🎉 All database tests passed!")
    else:
        print("\n❌ Some database tests failed!")


if __name__ == "__main__":
    asyncio.run(main())
