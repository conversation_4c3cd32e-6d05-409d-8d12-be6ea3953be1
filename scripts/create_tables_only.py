#!/usr/bin/env python3
"""Create database tables only (no sample data)."""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

# Import database components directly to avoid full mavia_tutor import
import sys
sys.path.insert(0, str(project_root / "src"))

from mavia_tutor.database.config import get_async_engine
from mavia_tutor.database.base import Base

# Import models individually to avoid circular imports
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress


async def create_tables():
    """Create all database tables."""
    engine = get_async_engine()
    
    async with engine.begin() as conn:
        print("🗑️ Dropping existing tables...")
        await conn.run_sync(Base.metadata.drop_all)
        
        print("🏗️ Creating new tables...")
        await conn.run_sync(Base.metadata.create_all)
    
    await engine.dispose()
    print("✅ Database tables created successfully!")


async def verify_tables():
    """Verify tables were created by listing them."""
    from sqlalchemy import text
    engine = get_async_engine()

    async with engine.connect() as conn:
        # Query to list all tables
        result = await conn.execute(
            text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
        )
        tables = [row[0] for row in result.fetchall()]

        print(f"\n📋 Created tables ({len(tables)}):")
        for table in sorted(tables):
            print(f"   - {table}")

    await engine.dispose()


async def main():
    """Main function."""
    print("🚀 Creating Mavia Math Tutor Database Tables...")
    
    # Check if database URL is configured
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable not set!")
        print("   Please set up your .env file with database configuration.")
        return
    
    print(f"🔗 Database URL: {database_url}")
    
    try:
        # Create tables
        await create_tables()
        
        # Verify tables
        await verify_tables()
        
        print("\n🎉 Table creation completed successfully!")
        
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
