#!/usr/bin/env python3
"""Initialize the database with tables and sample data."""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

# Import database components directly to avoid full mavia_tutor import
import sys
sys.path.insert(0, str(project_root / "src"))

from mavia_tutor.database.config import get_async_engine
from mavia_tutor.database.base import Base

# Import models individually to avoid circular imports
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress


async def create_tables_and_sample_data():
    """Create all database tables and sample data."""
    from sqlalchemy.ext.asyncio import async_sessionmaker
    from mavia_tutor.database.services.user_service import UserService
    from mavia_tutor.database.services.student_service import StudentService
    from mavia_tutor.database.services.curriculum_service import CurriculumService

    engine = get_async_engine()

    # Create tables
    async with engine.begin() as conn:
        # Drop all tables (for development)
        await conn.run_sync(Base.metadata.drop_all)

        # Create all tables
        await conn.run_sync(Base.metadata.create_all)

    print("✅ Database tables created successfully!")

    # Create session factory using the same engine
    session_factory = async_sessionmaker(engine, expire_on_commit=False)

    # Create sample data
    async with session_factory() as session:
        user_service = UserService(session)
        student_service = StudentService(session)
        curriculum_service = CurriculumService(session)
        
        # Create sample user
        user = await user_service.create_user(
            email="<EMAIL>",
            role="student",
            first_name="Test",
            last_name="Student",
            subscription_status="active"
        )
        
        # Create sample student profile
        student = await student_service.create_student_profile(
            user_id=user.id,
            student_id="test_student_001",
            name="Test Student",
            math_level="intermediate",
            age=15,
            grade_level="9th",
            goals=["Improve algebra skills", "Prepare for SAT"],
            strengths=["Problem solving", "Logical thinking"],
            challenges=["Word problems", "Geometry"]
        )
        
        # Create sample curriculum
        curriculum = await curriculum_service.create_curriculum(
            curriculum_id="math_basics_v1",
            name="Math Basics",
            description="Fundamental mathematics curriculum",
            version="1.0"
        )
        
        await session.commit()

        print("✅ Sample data created successfully!")
        print(f"   User: {user.email} (ID: {user.id})")
        print(f"   Student: {student.name} (ID: {student.student_id})")
        print(f"   Curriculum: {curriculum.name} (ID: {curriculum.curriculum_id})")

    # Close the engine
    await engine.dispose()


async def main():
    """Main initialization function."""
    print("🚀 Initializing Mavia Math Tutor Database...")
    
    # Check if database URL is configured
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("❌ DATABASE_URL environment variable not set!")
        print("   Please set up your .env file with database configuration.")
        return
    
    try:
        # Create tables and sample data
        await create_tables_and_sample_data()

        print("\n🎉 Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Start the backend server: python main.py")
        print("2. Test the API endpoints")
        print("3. Start the frontend: cd frontend && npm run dev")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
