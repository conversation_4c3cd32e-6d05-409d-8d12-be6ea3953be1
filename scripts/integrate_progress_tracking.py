#!/usr/bin/env python3
"""
Script to demonstrate and test progress tracking integration with the multi-agent system.

This script shows how to:
1. Initialize progress tracking for a session
2. Update progress during topic learning
3. Complete topics with mastery levels
4. End session with final progress updates
5. Suggest next topics based on progress

Usage:
    python scripts/integrate_progress_tracking.py
"""

import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from uuid import UUID

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession
from mavia_tutor.database.config import get_async_engine
from mavia_tutor.utils.progress_tracker import ProgressTracker

# Import services
from mavia_tutor.database.services.student_service import StudentService
from mavia_tutor.database.services.session_service import SessionService


async def demonstrate_progress_tracking():
    """Demonstrate progress tracking integration."""
    print("🚀 Demonstrating progress tracking integration")
    print("=" * 60)
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        # Initialize services
        student_service = StudentService(session)
        session_service = SessionService(session)
        progress_tracker = ProgressTracker(session)
        
        # Get a test student (or create one)
        students_data = await student_service.get_all_students(limit=1)
        if not students_data.students:
            print("❌ No students found. Please run database setup first.")
            return
        
        test_student = students_data.students[0]
        print(f"👤 Using test student: {test_student.name} ({test_student.student_id})")
        
        # Simulate a tutoring session
        session_id = f"demo_session_{test_student.student_id}_progress_tracking"
        target_topics = ["basic_arithmetic", "fractions_intro", "algebra_basics"]
        
        print(f"\n📚 Starting session: {session_id}")
        print(f"🎯 Target topics: {', '.join(target_topics)}")
        
        # Step 1: Start session tracking
        print("\n1️⃣ Initializing progress tracking...")
        await progress_tracker.start_session_tracking(
            student_id=test_student.id,
            session_id=session_id,
            target_topics=target_topics
        )
        print("   ✅ Progress tracking initialized")
        
        # Step 2: Simulate topic learning with progress updates
        print("\n2️⃣ Simulating topic learning...")
        
        # Topic 1: Basic Arithmetic
        print("   📖 Learning: Basic Arithmetic")
        await progress_tracker.update_topic_progress(
            student_id=test_student.id,
            topic_id="basic_arithmetic",
            time_spent_minutes=15.0,
            correct_answers=8
        )
        
        # Simulate assessment
        await progress_tracker.update_topic_progress(
            student_id=test_student.id,
            topic_id="basic_arithmetic",
            assessment_score=85.0,  # 85% score
            time_spent_minutes=5.0
        )
        
        # Complete the topic
        await progress_tracker.complete_topic(
            student_id=test_student.id,
            topic_id="basic_arithmetic",
            final_mastery_level=0.85,
            session_time_minutes=20.0
        )
        print("      ✅ Basic Arithmetic completed (85% mastery)")
        
        # Topic 2: Fractions Introduction
        print("   📖 Learning: Fractions Introduction")
        await progress_tracker.update_topic_progress(
            student_id=test_student.id,
            topic_id="fractions_intro",
            time_spent_minutes=25.0,
            correct_answers=6
        )
        
        # Simulate partial mastery
        await progress_tracker.complete_topic(
            student_id=test_student.id,
            topic_id="fractions_intro",
            final_mastery_level=0.65,
            session_time_minutes=25.0
        )
        print("      ✅ Fractions Introduction completed (65% mastery)")
        
        # Topic 3: Algebra Basics (just started)
        print("   📖 Starting: Algebra Basics")
        await progress_tracker.update_topic_progress(
            student_id=test_student.id,
            topic_id="algebra_basics",
            time_spent_minutes=10.0,
            correct_answers=2
        )
        print("      ⏳ Algebra Basics in progress")
        
        # Step 3: Get topic suggestions
        print("\n3️⃣ Getting next topic suggestions...")
        next_topics = await progress_tracker.suggest_next_topics(test_student.id)
        if next_topics:
            print(f"   🎯 Suggested next topics: {', '.join(next_topics)}")
        else:
            print("   📝 No specific suggestions (may need learning path assignment)")
        
        # Step 4: End session tracking
        print("\n4️⃣ Ending session tracking...")
        total_session_time = 60.0  # 1 hour session
        topics_covered = ["basic_arithmetic", "fractions_intro", "algebra_basics"]
        
        await progress_tracker.end_session_tracking(
            student_id=test_student.id,
            session_id=session_id,
            total_session_time_minutes=total_session_time,
            topics_covered=topics_covered
        )
        print("   ✅ Session tracking completed")
        
        # Step 5: Display final progress
        print("\n5️⃣ Final progress summary...")
        await display_student_progress(test_student.id, session)
    
    await engine.dispose()


async def display_student_progress(student_id: UUID, db_session: AsyncSession):
    """Display student's current progress."""
    from mavia_tutor.database.services.progress_service import ProgressService, TopicProgressService
    
    progress_service = ProgressService(db_session)
    topic_progress_service = TopicProgressService(db_session)
    
    # Get learning progress
    learning_progress = await progress_service.get_student_progress(student_id)
    if learning_progress:
        print(f"   📊 Total sessions: {learning_progress.total_sessions}")
        print(f"   ⏱️  Total time: {learning_progress.total_time_minutes} minutes")
        print(f"   🏆 Achievements: {len(learning_progress.achievements or [])}")
        if learning_progress.current_path_id:
            print(f"   🛤️  Current path: {learning_progress.current_path_id}")
    
    # Get topic progress
    topic_progress_list = await topic_progress_service.get_student_all_topic_progress(student_id)
    if topic_progress_list:
        print(f"   📚 Topics with progress: {len(topic_progress_list)}")
        for tp in topic_progress_list:
            mastery_percent = float(tp.mastery_level) * 100
            print(f"      - {tp.topic_name}: {mastery_percent:.1f}% mastery, {tp.attempts} attempts, {tp.time_spent_minutes}m")


async def test_integration_with_existing_sessions():
    """Test integration with existing session data."""
    print("\n🔧 Testing integration with existing sessions...")
    
    engine = get_async_engine()
    
    async with AsyncSession(engine) as session:
        student_service = StudentService(session)
        session_service = SessionService(session)
        progress_tracker = ProgressTracker(session)
        
        # Get all students with sessions
        students_data = await student_service.get_all_students(limit=10)
        
        for student in students_data.students:
            # Get student's recent sessions
            sessions_data = await session_service.get_student_sessions(student.id, limit=5)
            
            if sessions_data.sessions:
                print(f"\n👤 {student.name}: {len(sessions_data.sessions)} recent sessions")
                
                # Simulate progress tracking for the most recent session
                recent_session = sessions_data.sessions[0]
                
                if recent_session.target_topics:
                    print(f"   🎯 Topics: {', '.join(recent_session.target_topics)}")
                    
                    # Initialize tracking (safe to call multiple times)
                    await progress_tracker.start_session_tracking(
                        student_id=student.id,
                        session_id=recent_session.session_id,
                        target_topics=recent_session.target_topics
                    )
                    
                    # Simulate some progress updates
                    for topic in recent_session.target_topics[:2]:  # Process first 2 topics
                        if topic != 'general_learning':
                            estimated_mastery = min(0.8, recent_session.time_spent_minutes / 60.0)
                            await progress_tracker.update_topic_progress(
                                student_id=student.id,
                                topic_id=topic,
                                mastery_level=estimated_mastery,
                                time_spent_minutes=recent_session.time_spent_minutes / len(recent_session.target_topics)
                            )
                    
                    print(f"   ✅ Progress updated for recent session")
    
    await engine.dispose()


async def main():
    """Main function."""
    try:
        # Demonstrate progress tracking
        await demonstrate_progress_tracking()
        
        # Test with existing sessions
        await test_integration_with_existing_sessions()
        
        print("\n" + "=" * 60)
        print("✅ Progress tracking integration demonstration completed!")
        print("\n📋 Integration points for multi-agent system:")
        print("   1. Call progress_tracker.start_session_tracking() when session starts")
        print("   2. Call progress_tracker.update_topic_progress() during topic learning")
        print("   3. Call progress_tracker.complete_topic() when topic is mastered")
        print("   4. Call progress_tracker.end_session_tracking() when session ends")
        print("   5. Use progress_tracker.suggest_next_topics() for recommendations")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
