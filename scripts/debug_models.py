#!/usr/bin/env python3
"""Debug script to check model registration."""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Load environment variables from .env file
load_dotenv(project_root / ".env")

# Import database components
sys.path.insert(0, str(project_root / "src"))

from mavia_tutor.database.base import Base

print("🔍 Debugging model registration...")

# Import models to register them
print("Importing models...")
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress

print("✅ Models imported successfully")

# Check what tables are registered
print(f"\n📋 Registered tables in Base.metadata:")
for table_name, table in Base.metadata.tables.items():
    print(f"   - {table_name}: {table}")

print(f"\nTotal tables: {len(Base.metadata.tables)}")

# Check model classes
models = [User, StudentProfile, TutoringSession, ChatMessage, Curriculum, Topic, LearningPath, Assessment, Question, TestResult, LearningProgress, TopicProgress]
print(f"\n🏷️ Model classes:")
for model in models:
    print(f"   - {model.__name__}: {model.__tablename__}")

print("\n✅ Debug complete!")
