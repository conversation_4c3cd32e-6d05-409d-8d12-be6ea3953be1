#!/usr/bin/env python3
"""
Synchronous database setup script for Mavia Math Tutor.

This script:
1. Creates all database tables
2. Populates curriculum data from integrated curriculum file
3. Creates sample user and student data

Usage:
    python scripts/setup_database_sync.py
"""

import os
import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from mavia_tutor.database.base import Base
from mavia_tutor.database.config import get_database_url

# Import all models to register them
from mavia_tutor.database.models.user import User
from mavia_tutor.database.models.student import StudentProfile
from mavia_tutor.database.models.session import TutoringSession, ChatMessage
from mavia_tutor.database.models.curriculum import Curriculum, Topic, LearningPath
from mavia_tutor.database.models.assessment import Assessment, Question, TestResult
from mavia_tutor.database.models.progress import LearningProgress, TopicProgress


def create_tables():
    """Create all database tables."""
    print("🔧 Creating database tables...")
    
    # Get database URL and create engine
    database_url = get_database_url()
    # Convert async URL to sync URL
    if database_url.startswith("postgresql+asyncpg://"):
        database_url = database_url.replace("postgresql+asyncpg://", "postgresql://")
    
    engine = create_engine(database_url)
    
    # Drop and create all tables
    Base.metadata.drop_all(engine)
    print("   ✅ Dropped existing tables")
    
    Base.metadata.create_all(engine)
    print("   ✅ Created all tables")
    
    return engine


def populate_curriculum_data(engine):
    """Populate curriculum data from integrated curriculum file."""
    print("📚 Populating curriculum data...")
    
    # Load integrated curriculum data
    curriculum_file = project_root / "data" / "integrated_curriculum" / "integrated_curriculum.json"
    
    if not curriculum_file.exists():
        print(f"   ⚠️  Integrated curriculum file not found at {curriculum_file}")
        return
    
    with open(curriculum_file, 'r', encoding='utf-8') as f:
        curriculum_data = json.load(f)
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create curriculum
        curriculum = Curriculum(
            curriculum_id=curriculum_data['curriculum_id'],
            name=curriculum_data['name'],
            description=curriculum_data['description'],
            version=curriculum_data.get('version', '1.0'),
            is_active=True,
            curriculum_metadata={
                'source': 'integrated_curriculum',
                'topics_count': len(curriculum_data['topics']),
                'learning_paths_count': len(curriculum_data.get('learning_paths', {}))
            }
        )
        session.add(curriculum)
        session.flush()  # Get the curriculum ID
        
        # Create topics
        topics_data = curriculum_data.get('topics', {})
        for topic_id, topic_info in topics_data.items():
            topic = Topic(
                curriculum_id=curriculum.id,
                topic_id=topic_info['topic_id'],
                name=topic_info['name'],
                description=topic_info.get('description', ''),
                category=topic_info.get('category', 'general'),
                difficulty=topic_info.get('difficulty', 'medium'),
                prerequisites=topic_info.get('prerequisites', []),
                learning_objectives=topic_info.get('learning_objectives', []),
                key_concepts=topic_info.get('key_concepts', []),
                examples=topic_info.get('examples', []),
                estimated_time_minutes=topic_info.get('estimated_time_minutes', 60),
                content=topic_info
            )
            session.add(topic)
        
        # Create learning paths
        learning_paths_data = curriculum_data.get('learning_paths', {})
        for path_id, path_info in learning_paths_data.items():
            learning_path = LearningPath(
                curriculum_id=curriculum.id,
                path_id=path_info['path_id'],
                name=path_info['name'],
                description=path_info.get('description', ''),
                target_level=path_info.get('target_level', 'beginner'),
                topic_sequence=path_info.get('topic_sequence', []),
                estimated_duration_hours=path_info.get('estimated_duration_hours', 1),
                prerequisites=path_info.get('prerequisites', []),
                path_metadata=path_info
            )
            session.add(learning_path)
        
        session.commit()
        
        print(f"   ✅ Created curriculum: {curriculum.name}")
        print(f"   ✅ Created {len(topics_data)} topics")
        print(f"   ✅ Created {len(learning_paths_data)} learning paths")
        
    except Exception as e:
        session.rollback()
        print(f"   ❌ Failed to populate curriculum data: {e}")
        raise
    finally:
        session.close()


def create_sample_data(engine):
    """Create sample user and student data."""
    print("👤 Creating sample user and student data...")
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create sample user
        user = User(
            user_id="user_sample_123",
            email="<EMAIL>",
            first_name="Sample",
            last_name="User"
        )
        session.add(user)
        session.flush()  # Get the user ID
        
        # Create sample student profile
        student = StudentProfile(
            student_id="student_sample_123",
            user_id=user.id,
            name="Sample Student",
            age=12,
            math_level="intermediate",
            learning_style="visual",
            user_email="<EMAIL>"
        )
        session.add(student)
        session.flush()  # Get the student ID
        
        # Initialize learning progress
        progress = LearningProgress(
            student_id=student.id,
            current_path_id=None,
            total_sessions=0,
            total_time_minutes=0,
            achievements=[]
        )
        session.add(progress)
        
        session.commit()
        
        print(f"   ✅ Created user: {user.email} (ID: {user.id})")
        print(f"   ✅ Created student: {student.name} (ID: {student.student_id})")
        
    except Exception as e:
        session.rollback()
        print(f"   ❌ Failed to create sample data: {e}")
        raise
    finally:
        session.close()


def verify_setup(engine):
    """Verify that the database setup was successful."""
    print("🔍 Verifying database setup...")
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Check curricula
        curricula = session.query(Curriculum).filter(Curriculum.is_active == True).all()
        print(f"   📚 Found {len(curricula)} active curricula")
        
        for curriculum in curricula:
            topics_count = session.query(Topic).filter(Topic.curriculum_id == curriculum.id).count()
            paths_count = session.query(LearningPath).filter(LearningPath.curriculum_id == curriculum.id).count()
            print(f"      - {curriculum.name}: {topics_count} topics, {paths_count} learning paths")
        
        # Check students
        students = session.query(StudentProfile).limit(10).all()
        print(f"   👤 Found {len(students)} students")
        
        for student in students:
            print(f"      - {student.name} ({student.student_id})")
            
    finally:
        session.close()


def main():
    """Main setup function."""
    print("🚀 Starting synchronous database setup for Mavia Math Tutor")
    print("=" * 60)
    
    try:
        # Step 1: Create tables
        engine = create_tables()
        
        # Step 2: Populate curriculum data
        populate_curriculum_data(engine)
        
        # Step 3: Create sample data
        create_sample_data(engine)
        
        # Step 4: Verify setup
        verify_setup(engine)
        
        print("=" * 60)
        print("✅ Database setup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Start the application server")
        print("   2. Create additional users through the web interface")
        print("   3. Begin tutoring sessions to populate progress data")
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
