#!/usr/bin/env python3
"""
IXL Integration Demo for Mavia Math Tutor

This example demonstrates how to:
1. Scrape IXL curriculum data
2. Integrate it with <PERSON><PERSON>'s curriculum
3. Use the enhanced curriculum in tutoring sessions
4. Show the benefits of the expanded curriculum
"""

import asyncio
import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mavia_tutor.main import MaviaTutor
from mavia_tutor.scrapers.ixl_scraper import IXLScraper
from mavia_tutor.curriculum_integration import CurriculumIntegrator


async def demonstrate_ixl_scraping():
    """Demonstrate IXL curriculum scraping."""
    print("🕷️  IXL Curriculum Scraping Demonstration")
    print("=" * 50)
    
    # Initialize scraper
    output_dir = "./examples/demo_ixl_data"
    scraper = IXLScraper(output_dir)
    
    print("1. IXL Scraper Configuration:")
    print(f"   Base URL: {scraper.base_url}")
    print(f"   Output directory: {output_dir}")
    print(f"   Year groups to scrape: {len(scraper.year_groups)}")
    
    print("\n2. Available Year Groups:")
    for year_key, year_info in list(scraper.year_groups.items())[:5]:  # Show first 5
        print(f"   {year_key}: Ages {year_info['age_range']}, Level: {year_info['level']}")
    print(f"   ... and {len(scraper.year_groups) - 5} more year groups")
    
    print("\n3. Category Mapping:")
    for keyword, category in list(scraper.category_mapping.items())[:8]:  # Show first 8
        print(f"   '{keyword}' → {category.value}")
    print(f"   ... and {len(scraper.category_mapping) - 8} more mappings")
    
    # Note: Actual scraping would be done here in a real scenario
    print("\n💡 Note: In this demo, we're showing the scraper configuration.")
    print("   To actually scrape IXL data, run:")
    print("   python cli.py scrape-curriculum --dry-run")
    print("   python cli.py scrape-curriculum --integrate")


def demonstrate_curriculum_comparison():
    """Compare default curriculum with IXL-enhanced curriculum."""
    print("\n📚 Curriculum Comparison")
    print("=" * 50)
    
    # Load default curriculum
    tutor_default = MaviaTutor()
    default_curriculum = tutor_default.curriculum
    
    print("1. Default Mavia Curriculum:")
    print(f"   Topics: {len(default_curriculum.topics)}")
    print(f"   Learning paths: {len(default_curriculum.learning_paths)}")
    
    # Show topic categories
    category_counts = {}
    for topic in default_curriculum.topics.values():
        category = topic.category.value
        category_counts[category] = category_counts.get(category, 0) + 1
    
    print("   Topic categories:")
    for category, count in category_counts.items():
        print(f"     {category}: {count} topics")
    
    # Check for integrated curriculum
    integrated_path = Path("./data/integrated_curriculum/integrated_curriculum.json")
    
    if integrated_path.exists():
        print("\n2. IXL-Enhanced Curriculum:")
        try:
            with open(integrated_path, 'r', encoding='utf-8') as f:
                integrated_data = json.load(f)
            
            print(f"   Topics: {len(integrated_data.get('topics', {}))}")
            print(f"   Learning paths: {len(integrated_data.get('learning_paths', {}))}")
            
            # Calculate enhancement
            topic_increase = len(integrated_data.get('topics', {})) - len(default_curriculum.topics)
            path_increase = len(integrated_data.get('learning_paths', {})) - len(default_curriculum.learning_paths)
            
            print(f"   Enhancement: +{topic_increase} topics, +{path_increase} paths")
            
        except Exception as e:
            print(f"   Error loading integrated curriculum: {e}")
    else:
        print("\n2. IXL-Enhanced Curriculum: Not available")
        print("   Run 'python cli.py scrape-curriculum --integrate' to create it")


async def demonstrate_enhanced_tutoring():
    """Demonstrate tutoring with enhanced curriculum."""
    print("\n🎓 Enhanced Tutoring Demonstration")
    print("=" * 50)
    
    # Initialize tutor (will use integrated curriculum if available)
    tutor = MaviaTutor()
    
    print(f"1. Loaded curriculum: {tutor.curriculum.name}")
    print(f"   Total topics: {len(tutor.curriculum.topics)}")
    print(f"   Total learning paths: {len(tutor.curriculum.learning_paths)}")
    
    # Create a student to demonstrate enhanced features
    print("\n2. Creating student with specific needs...")
    student = tutor.create_student(
        student_id="ixl_demo_student",
        name="IXL Demo Student",
        age=11,
        grade_level="Year 6",
        math_level="elementary",
        learning_style="visual",
        goals=[
            "Master Year 6 mathematics",
            "Prepare for Year 7",
            "Improve problem-solving skills"
        ],
        strengths=["Visual learning", "Pattern recognition"],
        challenges=["Word problems", "Complex fractions"]
    )
    
    print(f"   Created: {student.profile.name}")
    print(f"   Goals: {', '.join(student.profile.goals)}")
    
    # Show available learning paths
    print("\n3. Available Learning Paths:")
    paths_by_level = {}
    for path_id, path in tutor.curriculum.learning_paths.items():
        level = path.target_level
        if level not in paths_by_level:
            paths_by_level[level] = []
        paths_by_level[level].append(path)
    
    for level, paths in paths_by_level.items():
        print(f"   {level.title()} level: {len(paths)} paths")
        for path in paths[:2]:  # Show first 2 paths per level
            print(f"     • {path.name} ({len(path.topic_sequence)} topics)")
    
    # Demonstrate topic selection
    print("\n4. Topic Selection for Year 6 Student:")
    year6_topics = []
    for topic_id, topic in tutor.curriculum.topics.items():
        # Look for Year 6 specific topics (if from IXL)
        if hasattr(topic, 'year_group') and 'year-6' in str(getattr(topic, 'year_group', '')):
            year6_topics.append(topic)
        elif 'year 6' in topic.name.lower() or 'grade 6' in topic.name.lower():
            year6_topics.append(topic)
    
    if year6_topics:
        print(f"   Found {len(year6_topics)} Year 6 specific topics:")
        for topic in year6_topics[:5]:  # Show first 5
            print(f"     • {topic.name}")
        if len(year6_topics) > 5:
            print(f"     ... and {len(year6_topics) - 5} more")
    else:
        print("   No Year 6 specific topics found (using default curriculum)")
    
    # Start a demo session
    print("\n5. Starting Enhanced Tutoring Session...")
    try:
        session_id = await tutor.start_tutoring_session(
            student_id="ixl_demo_student",
            session_goals=["Assess current level", "Practice key skills"],
            max_duration_minutes=20  # Short demo session
        )
        
        print(f"   ✅ Session completed: {session_id}")
        
        # Get session summary
        summary = tutor.get_session_summary(session_id)
        if summary:
            print(f"   Duration: {summary.get('duration_minutes', 0):.1f} minutes")
            print(f"   Topics covered: {summary.get('topics_completed', 0)}")
        
    except Exception as e:
        print(f"   ❌ Session error: {e}")


def show_integration_benefits():
    """Show the benefits of IXL integration."""
    print("\n🌟 Benefits of IXL Integration")
    print("=" * 50)
    
    benefits = [
        {
            "title": "Comprehensive Coverage",
            "description": "Access to thousands of skills across all year groups (Reception to Year 13)"
        },
        {
            "title": "Age-Appropriate Content", 
            "description": "Skills are mapped to specific age ranges and UK curriculum standards"
        },
        {
            "title": "Granular Skill Tracking",
            "description": "Track progress on specific skills rather than broad topic areas"
        },
        {
            "title": "Prerequisite Mapping",
            "description": "Automatic prerequisite detection based on year progression"
        },
        {
            "title": "Difficulty Progression",
            "description": "Skills are categorized by difficulty for optimal learning paths"
        },
        {
            "title": "Real-World Alignment",
            "description": "Content aligns with what students learn in UK schools"
        }
    ]
    
    for i, benefit in enumerate(benefits, 1):
        print(f"{i}. {benefit['title']}")
        print(f"   {benefit['description']}")
        print()
    
    print("💡 Next Steps:")
    print("• Run the scraper to get the latest IXL curriculum data")
    print("• Integrate with your existing Mavia curriculum")
    print("• Create students with specific year group needs")
    print("• Use year-specific learning paths for targeted learning")


async def main():
    """Main demonstration function."""
    try:
        print("🎓 Mavia Math Tutor - IXL Integration Demo")
        print("=" * 60)
        
        await demonstrate_ixl_scraping()
        demonstrate_curriculum_comparison()
        await demonstrate_enhanced_tutoring()
        show_integration_benefits()
        
        print("\n🎉 IXL Integration Demo Complete!")
        print("\nTo get started with IXL integration:")
        print("1. python cli.py scrape-curriculum --dry-run  # See what would be scraped")
        print("2. python cli.py scrape-curriculum --integrate  # Scrape and integrate")
        print("3. python cli.py create-student --student-id uk_student --name 'UK Student' --level elementary")
        print("4. python cli.py start-session --student-id uk_student")
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
