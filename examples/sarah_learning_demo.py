#!/usr/bin/env python3
"""
<PERSON> Learning Session Demo

This demo runs targeted learning sessions for <PERSON> with specific
algebra-focused topics to match her goals of mastering algebra and preparing
for calculus.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mavia_tutor.main import MaviaTutor


async def run_sarah_learning_sessions():
    """Run targeted learning sessions for <PERSON>."""
    print("🎓 <PERSON> Johnson - Targeted Learning Sessions")
    print("=" * 60)
    
    # Check environment
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found in environment")
        print("Please set your Gemini API key in the .env file")
        return
    
    try:
        # Initialize tutor
        print("🔧 Initializing Mavia Math Tutor...")
        tutor = MaviaTutor()
        print("✅ Tutor initialized successfully")
        
        # Get Sarah's profile
        student_id = "sarah_002"
        student = tutor.get_student(student_id)
        
        if not student:
            print(f"❌ Student {student_id} not found")
            print("Please create <PERSON> first using:")
            print('python cli.py create-student --student-id sarah_002 --name "<PERSON>" --age 13 --grade "8th" --level intermediate --style visual --goals "Master algebra,Prepare for calculus" --strengths "Pattern recognition,Logical thinking" --challenges "Word problems,Test anxiety"')
            return
        
        print(f"\n👤 Student Profile: {student.profile.name}")
        print(f"   📊 Level: {student.profile.math_level.value}")
        print(f"   🎯 Goals: {', '.join(student.profile.goals)}")
        print(f"   💪 Strengths: {', '.join(student.profile.strengths)}")
        print(f"   ⚠️  Challenges: {', '.join(student.profile.challenges)}")
        
        # Show current progress
        print(f"\n📈 Current Progress:")
        print(f"   Total Sessions: {student.progress.total_sessions}")
        print(f"   Total Time: {student.progress.total_time_minutes:.1f} minutes")
        print(f"   Topics Practiced: {len(student.progress.topics_progress)}")
        
        # Define algebra-focused learning sequence for Sarah
        algebra_topics = [
            "algebra_basics",           # Start with algebra fundamentals
            "linear_equations",         # Build to linear equations
            "basic_arithmetic"          # Reinforce arithmetic skills
        ]
        
        print(f"\n🎯 Planned Learning Sequence:")
        for i, topic_id in enumerate(algebra_topics, 1):
            topic = tutor.curriculum.get_topic(topic_id)
            topic_name = topic.name if topic else topic_id
            print(f"   {i}. {topic_name}")
        
        # Run learning sessions
        session_results = []
        
        for i, topic_id in enumerate(algebra_topics, 1):
            topic = tutor.curriculum.get_topic(topic_id)
            topic_name = topic.name if topic else topic_id
            
            print(f"\n🎓 Learning Session {i}: {topic_name}")
            print("-" * 50)
            
            try:
                # Start targeted session
                session_goals = [
                    f"Master {topic_name}",
                    "Build algebra foundation",
                    "Prepare for advanced topics"
                ]
                
                print(f"🚀 Starting session with goals: {', '.join(session_goals)}")
                
                session_id = await tutor.start_tutoring_session(
                    student_id=student_id,
                    target_topics=[topic_id],
                    max_duration_minutes=25,
                    session_goals=session_goals
                )
                
                print(f"✅ Session completed: {session_id}")
                
                # Get updated progress
                updated_student = tutor.get_student(student_id)
                if updated_student and topic_id in updated_student.progress.topics_progress:
                    progress = updated_student.progress.topics_progress[topic_id]
                    mastery_percent = progress.mastery_level * 100
                    accuracy = (progress.correct_answers / progress.attempts * 100) if progress.attempts > 0 else 0
                    
                    print(f"📊 Progress Update:")
                    print(f"   Mastery: {mastery_percent:.1f}%")
                    print(f"   Accuracy: {accuracy:.1f}%")
                    print(f"   Time Spent: {progress.time_spent_minutes:.1f} minutes")
                    
                    session_results.append({
                        "topic": topic_name,
                        "mastery": mastery_percent,
                        "accuracy": accuracy,
                        "time": progress.time_spent_minutes,
                        "success": True
                    })
                else:
                    print("📊 No progress data recorded")
                    session_results.append({
                        "topic": topic_name,
                        "success": False
                    })
                
                # Brief pause between sessions
                if i < len(algebra_topics):
                    print("⏸️  Pausing between sessions...")
                    await asyncio.sleep(2)
                    
            except Exception as e:
                print(f"❌ Session failed: {str(e)}")
                session_results.append({
                    "topic": topic_name,
                    "success": False,
                    "error": str(e)
                })
        
        # Final progress analysis
        print(f"\n📊 Final Progress Analysis")
        print("-" * 50)
        
        final_student = tutor.get_student(student_id)
        if final_student:
            print(f"📈 Overall Progress:")
            print(f"   Total Sessions: {final_student.progress.total_sessions}")
            print(f"   Total Time: {final_student.progress.total_time_minutes:.1f} minutes")
            print(f"   Topics Practiced: {len(final_student.progress.topics_progress)}")
            
            if final_student.progress.topics_progress:
                print(f"\n🎯 Topic Mastery Levels:")
                for topic_id, progress in final_student.progress.topics_progress.items():
                    mastery_percent = progress.mastery_level * 100
                    accuracy = (progress.correct_answers / progress.attempts * 100) if progress.attempts > 0 else 0
                    
                    status = "✅" if mastery_percent >= 80 else "📚" if mastery_percent >= 50 else "⚠️"
                    print(f"   {status} {progress.topic_name}: {mastery_percent:.1f}% mastery, {accuracy:.1f}% accuracy")
            
            # Get curriculum recommendations
            curriculum = tutor.curriculum
            mastered_topics = set()
            for topic_id, progress in final_student.progress.topics_progress.items():
                if progress.mastery_level >= 0.8:
                    mastered_topics.add(topic_id)
            
            available_topics = curriculum.get_available_topics(mastered_topics)
            
            print(f"\n🎯 Recommended Next Topics:")
            algebra_related = []
            for topic in available_topics:
                # Filter for algebra-related topics
                if any(keyword in topic.name.lower() for keyword in ['algebra', 'equation', 'variable', 'expression']):
                    algebra_related.append(topic)
            
            if algebra_related:
                for i, topic in enumerate(algebra_related[:5], 1):
                    prereq_status = "✅" if topic.has_prerequisites_met(mastered_topics) else "⏳"
                    print(f"   {i}. {prereq_status} {topic.name} ({topic.difficulty.value})")
            else:
                print("   Continue with current algebra topics or explore:")
                for i, topic in enumerate(available_topics[:5], 1):
                    prereq_status = "✅" if topic.has_prerequisites_met(mastered_topics) else "⏳"
                    print(f"   {i}. {prereq_status} {topic.name} ({topic.difficulty.value})")
        
        # Session summary
        print(f"\n🎉 Learning Sessions Complete!")
        print("-" * 50)
        successful_sessions = sum(1 for r in session_results if r.get('success', False))
        print(f"✅ Successful sessions: {successful_sessions}/{len(session_results)}")
        
        if successful_sessions > 0:
            avg_mastery = sum(r.get('mastery', 0) for r in session_results if r.get('success', False)) / successful_sessions
            total_time = sum(r.get('time', 0) for r in session_results if r.get('success', False))
            print(f"📊 Average mastery: {avg_mastery:.1f}%")
            print(f"⏱️  Total learning time: {total_time:.1f} minutes")
        
        print(f"\n💾 Sarah's progress saved to: data/students/{student_id}.json")
        print(f"🎓 Sarah is making progress toward her goals of mastering algebra!")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(run_sarah_learning_sessions())
