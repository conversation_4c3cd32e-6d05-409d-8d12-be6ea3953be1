#!/usr/bin/env python3
"""
Custom curriculum example for the Mavia Math Tutor system.

This example demonstrates how to:
1. Create custom topics and learning paths
2. Extend the default curriculum
3. Save and load custom curricula
4. Use custom content in tutoring sessions
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.mavia_tutor.main import MaviaTutor
from src.mavia_tutor.utils import CurriculumLoader
from src.mavia_tutor.models.curriculum import Topic, LearningPath, TopicCategory, Difficulty


def create_custom_topics():
    """Create custom math topics."""
    
    print("📚 Creating custom topics...")
    
    # Advanced Geometry Topic
    advanced_geometry = Topic(
        topic_id="advanced_geometry",
        name="Advanced Geometry",
        description="Advanced geometric concepts including coordinate geometry and transformations",
        category=TopicCategory.GEOMETRY,
        difficulty=Difficulty.HARD,
        prerequisites=["basic_geometry", "algebra_basics"],
        learning_objectives=[
            "Master coordinate geometry concepts",
            "Understand geometric transformations",
            "Apply geometry to real-world problems",
            "Work with advanced geometric proofs"
        ],
        key_concepts=[
            "Coordinate plane",
            "Distance formula",
            "Midpoint formula", 
            "Transformations",
            "Congruence and similarity"
        ],
        estimated_time_minutes=90,
        examples=[
            "Distance between (2,3) and (5,7)",
            "Reflection across y-axis",
            "Rotation of 90 degrees",
            "Triangle congruence proofs"
        ]
    )
    
    # Statistics and Probability Topic
    statistics_probability = Topic(
        topic_id="statistics_probability",
        name="Statistics and Probability",
        description="Introduction to statistical analysis and probability concepts",
        category=TopicCategory.STATISTICS,
        difficulty=Difficulty.MEDIUM,
        prerequisites=["percentages", "fractions_operations"],
        learning_objectives=[
            "Understand basic statistical measures",
            "Calculate probability of events",
            "Interpret data and graphs",
            "Make predictions based on data"
        ],
        key_concepts=[
            "Mean, median, mode",
            "Range and standard deviation",
            "Probability rules",
            "Data visualization",
            "Sampling and surveys"
        ],
        estimated_time_minutes=75,
        examples=[
            "Calculate mean of dataset",
            "Probability of rolling a 6",
            "Interpret bar graphs",
            "Survey data analysis"
        ]
    )
    
    # Pre-Calculus Topic
    precalculus = Topic(
        topic_id="precalculus",
        name="Pre-Calculus",
        description="Advanced algebra and trigonometry preparing for calculus",
        category=TopicCategory.ALGEBRA,
        difficulty=Difficulty.VERY_HARD,
        prerequisites=["linear_equations", "advanced_geometry"],
        learning_objectives=[
            "Master polynomial and rational functions",
            "Understand exponential and logarithmic functions",
            "Work with trigonometric functions",
            "Prepare for calculus concepts"
        ],
        key_concepts=[
            "Function composition",
            "Inverse functions",
            "Exponential growth/decay",
            "Logarithmic properties",
            "Trigonometric identities"
        ],
        estimated_time_minutes=120,
        examples=[
            "f(g(x)) composition",
            "Solve exponential equations",
            "Trigonometric identities",
            "Function transformations"
        ]
    )
    
    # Problem Solving Strategies Topic
    problem_solving = Topic(
        topic_id="problem_solving_strategies",
        name="Problem Solving Strategies",
        description="Mathematical problem-solving techniques and strategies",
        category=TopicCategory.DISCRETE_MATH,
        difficulty=Difficulty.MEDIUM,
        prerequisites=["algebra_basics"],
        learning_objectives=[
            "Learn systematic problem-solving approaches",
            "Apply multiple strategies to complex problems",
            "Develop mathematical reasoning skills",
            "Build confidence in tackling new problems"
        ],
        key_concepts=[
            "Polya's problem-solving steps",
            "Working backwards",
            "Pattern recognition",
            "Logical reasoning",
            "Mathematical modeling"
        ],
        estimated_time_minutes=60,
        examples=[
            "Age word problems",
            "Optimization problems",
            "Logic puzzles",
            "Real-world applications"
        ]
    )
    
    return {
        "advanced_geometry": advanced_geometry,
        "statistics_probability": statistics_probability,
        "precalculus": precalculus,
        "problem_solving_strategies": problem_solving
    }


def create_custom_learning_paths(custom_topics):
    """Create custom learning paths using the new topics."""
    
    print("🛤️  Creating custom learning paths...")
    
    # Advanced Math Path
    advanced_path = LearningPath(
        path_id="advanced_math",
        name="Advanced Mathematics",
        description="Comprehensive advanced math curriculum for high school students",
        target_level="advanced",
        topic_sequence=[
            "linear_equations",
            "advanced_geometry", 
            "statistics_probability",
            "precalculus"
        ],
        estimated_duration_hours=6.5,
        prerequisites=["algebra_basics", "basic_geometry"]
    )
    
    # Problem Solving Focus Path
    problem_solving_path = LearningPath(
        path_id="problem_solving_focus",
        name="Problem Solving Mastery",
        description="Focused curriculum on mathematical problem-solving skills",
        target_level="intermediate",
        topic_sequence=[
            "algebra_basics",
            "problem_solving_strategies",
            "statistics_probability",
            "advanced_geometry"
        ],
        estimated_duration_hours=5.0,
        prerequisites=["fractions_operations", "percentages"]
    )
    
    # STEM Preparation Path
    stem_prep_path = LearningPath(
        path_id="stem_preparation",
        name="STEM Preparation Track",
        description="Mathematics preparation for STEM fields",
        target_level="advanced",
        topic_sequence=[
            "algebra_basics",
            "linear_equations",
            "advanced_geometry",
            "statistics_probability",
            "problem_solving_strategies",
            "precalculus"
        ],
        estimated_duration_hours=8.0,
        prerequisites=["percentages", "basic_geometry"]
    )
    
    return {
        "advanced_math": advanced_path,
        "problem_solving_focus": problem_solving_path,
        "stem_preparation": stem_prep_path
    }


async def demonstrate_custom_curriculum():
    """Demonstrate using custom curriculum in tutoring sessions."""
    
    print("\n" + "=" * 60)
    print("🎓 Custom Curriculum Demonstration")
    print("=" * 60)
    
    # Create curriculum loader
    loader = CurriculumLoader("./examples/custom_curriculum_data")
    
    # Load default curriculum
    print("1. Loading default curriculum...")
    curriculum = loader.load_default_curriculum()
    print(f"   Default curriculum has {len(curriculum.topics)} topics")
    
    # Add custom topics
    print("\n2. Adding custom topics...")
    custom_topics = create_custom_topics()
    
    for topic_id, topic in custom_topics.items():
        curriculum.topics[topic_id] = topic
        print(f"   ✅ Added topic: {topic.name}")
    
    # Add custom learning paths
    print("\n3. Adding custom learning paths...")
    custom_paths = create_custom_learning_paths(custom_topics)
    
    for path_id, path in custom_paths.items():
        curriculum.learning_paths[path_id] = path
        print(f"   ✅ Added path: {path.name}")
    
    # Save the extended curriculum
    print("\n4. Saving extended curriculum...")
    success = loader.save_curriculum(curriculum, "extended_curriculum.json")
    if success:
        print("   ✅ Extended curriculum saved successfully")
    else:
        print("   ❌ Failed to save curriculum")
    
    # Initialize tutor with custom curriculum
    print("\n5. Initializing tutor with custom curriculum...")
    tutor = MaviaTutor(curriculum_path="./examples/custom_curriculum_data")
    
    # Create an advanced student
    print("\n6. Creating advanced student...")
    student = tutor.create_student(
        student_id="advanced_student_001",
        name="Advanced Student",
        age=16,
        grade_level="11th",
        math_level="advanced",
        learning_style="reading_writing",
        goals=[
            "Master advanced geometry",
            "Learn statistics and probability",
            "Prepare for calculus"
        ],
        strengths=[
            "Analytical thinking",
            "Mathematical reasoning",
            "Problem solving"
        ],
        challenges=[
            "Complex word problems",
            "Time management in tests"
        ]
    )
    
    print(f"   ✅ Created student: {student.profile.name}")
    
    # Start session with custom curriculum
    print("\n7. Starting tutoring session with custom topics...")
    
    session_id = await tutor.start_tutoring_session(
        student_id="advanced_student_001",
        target_topics=["advanced_geometry", "statistics_probability"],
        session_goals=[
            "Learn coordinate geometry",
            "Understand basic statistics",
            "Practice problem solving"
        ],
        max_duration_minutes=60
    )
    
    print(f"   ✅ Completed advanced session: {session_id}")
    
    # Show session results
    summary = tutor.get_session_summary(session_id)
    if summary:
        print(f"\n📊 Session Summary:")
        print(f"   Duration: {summary.get('duration_minutes', 0):.1f} minutes")
        print(f"   Topics completed: {summary.get('topics_completed', 0)}")
        print(f"   Status: {'Active' if summary.get('is_active', False) else 'Completed'}")
    
    # Show available custom paths
    print(f"\n8. Available custom learning paths:")
    for path_id, path in custom_paths.items():
        print(f"   • {path.name}")
        print(f"     Target Level: {path.target_level}")
        print(f"     Duration: {path.estimated_duration_hours} hours")
        print(f"     Topics: {len(path.topic_sequence)}")
        print()


async def demonstrate_curriculum_customization():
    """Show different ways to customize the curriculum."""
    
    print("\n" + "=" * 60)
    print("🔧 Curriculum Customization Options")
    print("=" * 60)
    
    loader = CurriculumLoader()
    curriculum = loader.load_default_curriculum()
    
    print("1. Topic Difficulty Adjustment:")
    print("   You can modify existing topics to adjust difficulty...")
    
    # Example: Make fractions easier
    fractions_topic = curriculum.get_topic("fractions_intro")
    if fractions_topic:
        original_difficulty = fractions_topic.difficulty
        fractions_topic.difficulty = Difficulty.EASY
        fractions_topic.estimated_time_minutes = 45  # Reduce time
        print(f"   ✅ Adjusted 'fractions_intro' from {original_difficulty.value} to {fractions_topic.difficulty.value}")
    
    print("\n2. Prerequisites Modification:")
    print("   You can change topic prerequisites to create different learning flows...")
    
    # Example: Remove some prerequisites to make algebra more accessible
    algebra_topic = curriculum.get_topic("algebra_basics")
    if algebra_topic:
        original_prereqs = algebra_topic.prerequisites.copy()
        algebra_topic.prerequisites = ["basic_arithmetic"]  # Simplified
        print(f"   ✅ Simplified algebra prerequisites from {original_prereqs} to {algebra_topic.prerequisites}")
    
    print("\n3. Learning Objectives Customization:")
    print("   You can modify learning objectives to focus on specific skills...")
    
    # Example: Add practical objectives to percentages
    percentages_topic = curriculum.get_topic("percentages")
    if percentages_topic:
        percentages_topic.learning_objectives.extend([
            "Calculate tips and discounts in real situations",
            "Understand interest rates and financial percentages",
            "Interpret percentage data in news and media"
        ])
        print(f"   ✅ Added practical objectives to percentages topic")
    
    print("\n4. Custom Learning Path Creation:")
    print("   You can create specialized paths for different goals...")
    
    # Example: Create a "Real World Math" path
    real_world_path = LearningPath(
        path_id="real_world_math",
        name="Real World Mathematics",
        description="Mathematics focused on practical, everyday applications",
        target_level="elementary",
        topic_sequence=[
            "basic_arithmetic",
            "decimals_basic", 
            "percentages",
            "basic_geometry"
        ],
        estimated_duration_hours=3.5,
        prerequisites=[]
    )
    
    curriculum.learning_paths["real_world_math"] = real_world_path
    print(f"   ✅ Created 'Real World Mathematics' learning path")
    
    print("\n5. Topic Categories and Organization:")
    available_categories = list(TopicCategory)
    print(f"   Available topic categories: {[cat.value for cat in available_categories]}")
    
    print("\n6. Difficulty Levels:")
    available_difficulties = list(Difficulty)
    print(f"   Available difficulty levels: {[diff.value for diff in available_difficulties]}")
    
    print("\n💡 Customization Tips:")
    print("   • Start with default curriculum and modify gradually")
    print("   • Test custom topics with sample students")
    print("   • Consider prerequisite chains when adding topics")
    print("   • Balance difficulty progression in learning paths")
    print("   • Save custom curricula for reuse")


if __name__ == "__main__":
    async def main():
        try:
            await demonstrate_custom_curriculum()
            await demonstrate_curriculum_customization()
            
            print("\n🎉 Custom curriculum examples completed!")
            print("\nNext steps:")
            print("• Experiment with your own custom topics")
            print("• Create specialized learning paths for different goals")
            print("• Test custom curricula with different student profiles")
            
        except KeyboardInterrupt:
            print("\n\n👋 Example interrupted by user")
        except Exception as e:
            print(f"\n❌ Error running example: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(main())
