#!/usr/bin/env python3
"""
Interactive Chat Demo

This demo shows how the interactive chat interface works by simulating
user inputs and showing agent responses.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mavia_tutor.main import MaviaTutor


async def simulate_interactive_chat():
    """Simulate an interactive chat session with predefined user inputs."""
    print("🎓 Interactive Chat Demo - Simulated Conversation")
    print("=" * 60)
    
    # Check environment
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found in environment")
        print("Please set your Gemini API key in the .env file")
        return
    
    try:
        # Initialize tutor
        print("🔧 Initializing Mavia Math Tutor...")
        tutor = MaviaTutor()
        
        # Get <PERSON>'s profile
        student_id = "sarah_002"
        student = tutor.get_student(student_id)
        
        if not student:
            print(f"❌ Student {student_id} not found")
            return
        
        print(f"\n👤 Student: {student.profile.name}")
        print(f"📊 Level: {student.profile.math_level.value}")
        print(f"🎯 Goals: {', '.join(student.profile.goals)}")
        print()
        
        # Create session manually for simulation
        session = tutor.session_manager.create_session(
            student=student,
            target_topics=["algebra_basics"],
            max_duration_minutes=30
        )
        
        # Create ADK context
        from google.adk.sessions.in_memory_session_service import InMemorySessionService
        from google.adk.sessions import Session
        from google.adk.agents.invocation_context import InvocationContext
        import uuid
        
        adk_session = Session(
            id=session.session_id,
            appName="mavia-math-tutor",
            userId=student_id
        )
        
        # Set up session state
        adk_session.state["student_profile"] = {
            "name": student.profile.name,
            "math_level": student.profile.math_level.value,
            "learning_style": student.profile.learning_style.value if student.profile.learning_style else "visual",
            "goals": student.profile.goals,
            "strengths": student.profile.strengths,
            "challenges": student.profile.challenges
        }
        
        adk_session.state["session_state"] = {"current_phase": "interactive_chat"}
        
        context = InvocationContext(
            session_service=InMemorySessionService(),
            invocation_id=str(uuid.uuid4()),
            agent=tutor.tutor_agent,
            session=adk_session
        )
        
        print("💬 Starting Interactive Chat Simulation")
        print("-" * 40)
        
        # Simulate conversation
        user_messages = [
            "Hi! I need help with algebra",
            "I'm confused about solving equations with x",
            "Can you explain how to isolate x?",
            "That makes sense! Can I try a practice problem?",
            "Thanks! This is really helpful"
        ]
        
        for i, user_message in enumerate(user_messages, 1):
            print(f"\n👤 User: {user_message}")
            
            # Set user message in context
            context.session.state["last_user_message"] = user_message
            
            # Get agent response
            print("🤖 TutorAgent: ", end="")
            
            response_parts = []
            async for event in tutor.tutor_agent._run_async_impl(context):
                if event.content and event.content.parts:
                    for part in event.content.parts:
                        if part.text:
                            response_parts.append(part.text)
                            print(part.text, end="")
            
            print()  # New line after response
            
            # Small delay for realism
            await asyncio.sleep(1)
        
        # End conversation
        print(f"\n👤 User: quit")
        print("🤖 TutorAgent: Great session! Keep practicing and see you next time!")
        
        # End session
        tutor.session_manager.end_session(session.session_id)
        
        print(f"\n📊 Demo Complete!")
        print(f"   Session ID: {session.session_id}")
        print(f"   Messages exchanged: {len(user_messages)}")
        print(f"   Interactive chat functionality demonstrated ✅")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(simulate_interactive_chat())
