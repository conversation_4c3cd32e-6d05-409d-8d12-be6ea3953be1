#!/usr/bin/env python3
"""
Comprehensive Demo: Curriculum Storage, Retrieval, and Progress Tracking

This example demonstrates the complete flow of how curriculum data is stored,
retrieved, and used in learning sessions, plus how student progress is tracked.
"""

import asyncio
import sys
import json
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mavia_tutor.main import MaviaTutor
from mavia_tutor.models.student import MathLevel, LearningStyle


async def demonstrate_curriculum_flow():
    """Demonstrate the complete curriculum and progress tracking flow."""
    
    print("🎓 Mavia Math Tutor - Curriculum & Progress Flow Demo")
    print("=" * 60)
    
    # Initialize the tutor system
    print("\n1. 📚 CURRICULUM LOADING")
    print("-" * 30)
    
    tutor = MaviaTutor()
    curriculum = tutor.curriculum
    
    print(f"✅ Loaded: {curriculum.name}")
    print(f"   📖 Topics: {len(curriculum.topics)}")
    print(f"   🛤️  Learning Paths: {len(curriculum.learning_paths)}")
    
    # Show curriculum structure
    print(f"\n📊 Curriculum Breakdown:")
    
    # Count by category
    category_counts = {}
    difficulty_counts = {}
    ixl_count = 0
    
    for topic_id, topic in curriculum.topics.items():
        # Category count
        category = topic.category.value
        category_counts[category] = category_counts.get(category, 0) + 1
        
        # Difficulty count
        difficulty = topic.difficulty.value
        difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
        
        # IXL count
        if topic_id.startswith('ixl_'):
            ixl_count += 1
    
    print(f"   📚 Categories:")
    for category, count in category_counts.items():
        print(f"     • {category.title()}: {count} topics")
    
    print(f"   📈 Difficulty Levels:")
    for difficulty, count in difficulty_counts.items():
        print(f"     • {difficulty.title()}: {count} topics")
    
    print(f"   🌐 IXL Topics: {ixl_count}")
    print(f"   🏠 Mavia Topics: {len(curriculum.topics) - ixl_count}")
    
    # Show learning paths
    print(f"\n🛤️  Learning Paths:")
    for path_id, path in curriculum.learning_paths.items():
        print(f"   • {path.name}")
        print(f"     Target: {path.target_level} | Topics: {len(path.topic_sequence)} | Duration: {path.estimated_duration_hours:.1f}h")
    
    # Create a student to demonstrate progress tracking
    print(f"\n2. 👤 STUDENT CREATION & PROFILE")
    print("-" * 30)
    
    student = tutor.create_student(
        student_id="demo_student_001",
        name="Alex Johnson",
        age=12,
        grade_level="Year 7",
        math_level=MathLevel.ELEMENTARY,
        learning_style=LearningStyle.VISUAL,
        goals=["Master fractions", "Prepare for Year 8", "Improve problem-solving"],
        strengths=["Visual learning", "Pattern recognition"],
        challenges=["Word problems", "Time management"]
    )
    
    print(f"✅ Created: {student.profile.name}")
    print(f"   🎯 Level: {student.profile.math_level.value}")
    print(f"   🎨 Style: {student.profile.learning_style.value}")
    print(f"   📋 Goals: {', '.join(student.profile.goals)}")
    print(f"   💪 Strengths: {', '.join(student.profile.strengths)}")
    print(f"   ⚠️  Challenges: {', '.join(student.profile.challenges)}")
    
    # Show initial progress
    print(f"\n📊 Initial Progress:")
    print(f"   Sessions: {student.progress.total_sessions}")
    print(f"   Time: {student.progress.total_time_minutes} minutes")
    print(f"   Topics Progress: {len(student.progress.topics_progress)} topics")
    
    # Simulate some learning progress
    print(f"\n3. 📈 SIMULATING LEARNING PROGRESS")
    print("-" * 30)
    
    # Simulate progress on several topics
    progress_data = [
        {"topic_id": "basic_arithmetic", "topic_name": "Basic Arithmetic", "correct": True, "time": 15.5},
        {"topic_id": "basic_arithmetic", "topic_name": "Basic Arithmetic", "correct": True, "time": 12.0},
        {"topic_id": "basic_arithmetic", "topic_name": "Basic Arithmetic", "correct": False, "time": 18.0},
        {"topic_id": "fractions_intro", "topic_name": "Introduction to Fractions", "correct": True, "time": 25.0},
        {"topic_id": "fractions_intro", "topic_name": "Introduction to Fractions", "correct": True, "time": 20.0},
        {"topic_id": "decimals_basic", "topic_name": "Basic Decimals", "correct": False, "time": 30.0},
        {"topic_id": "ixl_yR_identify_numbers_-_up_to_3", "topic_name": "Identify numbers - up to 3", "correct": True, "time": 8.0},
    ]
    
    print("Simulating practice sessions...")
    for i, data in enumerate(progress_data, 1):
        success = tutor.student_manager.update_topic_progress(
            student_id="demo_student_001",
            topic_id=data["topic_id"],
            topic_name=data["topic_name"],
            correct=data["correct"],
            time_spent=data["time"]
        )
        
        if success:
            result = "✅" if data["correct"] else "❌"
            print(f"   {i}. {result} {data['topic_name']} ({data['time']}min)")
    
    # Get updated student data
    updated_student = tutor.get_student("demo_student_001")
    
    print(f"\n📊 Updated Progress:")
    print(f"   Total Time: {updated_student.progress.total_time_minutes:.1f} minutes")
    print(f"   Topics Practiced: {len(updated_student.progress.topics_progress)}")
    
    # Show detailed topic progress
    print(f"\n📈 Topic Mastery Levels:")
    for topic_id, progress in updated_student.progress.topics_progress.items():
        mastery_percent = progress.mastery_level * 100
        accuracy = (progress.correct_answers / progress.attempts * 100) if progress.attempts > 0 else 0
        
        # Get topic name from curriculum
        topic = curriculum.get_topic(topic_id)
        topic_name = topic.name if topic else progress.topic_name
        
        print(f"   • {topic_name}")
        print(f"     Mastery: {mastery_percent:.1f}% | Accuracy: {accuracy:.1f}% | Attempts: {progress.attempts} | Time: {progress.time_spent_minutes:.1f}min")
    
    # Demonstrate curriculum-based topic selection
    print(f"\n4. 🎯 CURRICULUM-BASED TOPIC SELECTION")
    print("-" * 30)
    
    # Get mastered topics
    mastered_topics = set()
    for topic_id, progress in updated_student.progress.topics_progress.items():
        if progress.mastery_level >= 0.8:  # 80% mastery threshold
            mastered_topics.add(topic_id)
    
    print(f"Mastered Topics: {len(mastered_topics)}")
    for topic_id in mastered_topics:
        topic = curriculum.get_topic(topic_id)
        if topic:
            print(f"   ✅ {topic.name}")
    
    # Get available topics (prerequisites met)
    available_topics = curriculum.get_available_topics(mastered_topics)
    
    print(f"\nAvailable Next Topics: {len(available_topics)}")
    for topic in available_topics[:5]:  # Show first 5
        prereq_status = "✅" if topic.has_prerequisites_met(mastered_topics) else "⏳"
        print(f"   {prereq_status} {topic.name} ({topic.difficulty.value})")
        if topic.prerequisites:
            print(f"      Prerequisites: {', '.join(topic.prerequisites)}")
    
    # Suggest learning path
    suggested_path = curriculum.suggest_learning_path(
        current_level=updated_student.profile.math_level.value,
        goals=updated_student.profile.goals
    )
    
    if suggested_path:
        print(f"\n🛤️  Suggested Learning Path: {suggested_path.name}")
        print(f"   Target Level: {suggested_path.target_level}")
        print(f"   Duration: {suggested_path.estimated_duration_hours:.1f} hours")
        
        # Show progress on this path
        completed_in_path = mastered_topics.intersection(set(suggested_path.topic_sequence))
        progress_percent = suggested_path.get_progress_percentage(mastered_topics)
        
        print(f"   Progress: {progress_percent:.1f}% ({len(completed_in_path)}/{len(suggested_path.topic_sequence)} topics)")
        
        # Show next topic in path
        next_topic_id = suggested_path.get_next_topic(mastered_topics)
        if next_topic_id:
            next_topic = curriculum.get_topic(next_topic_id)
            if next_topic:
                print(f"   Next Topic: {next_topic.name}")
    
    # Show data persistence
    print(f"\n5. 💾 DATA PERSISTENCE")
    print("-" * 30)
    
    # Show where data is stored
    student_file = Path(f"./data/students/demo_student_001.json")
    curriculum_file = Path("./data/integrated_curriculum/integrated_curriculum.json")
    
    print(f"Student Data: {student_file}")
    print(f"   Exists: {'✅' if student_file.exists() else '❌'}")
    if student_file.exists():
        print(f"   Size: {student_file.stat().st_size:,} bytes")
    
    print(f"Curriculum Data: {curriculum_file}")
    print(f"   Exists: {'✅' if curriculum_file.exists() else '❌'}")
    if curriculum_file.exists():
        print(f"   Size: {curriculum_file.stat().st_size:,} bytes")
    
    # Show student data structure
    if student_file.exists():
        with open(student_file, 'r') as f:
            student_data = json.load(f)
        
        print(f"\n📄 Student Data Structure:")
        print(f"   Profile Fields: {len(student_data['profile'])}")
        print(f"   Progress Fields: {len(student_data['progress'])}")
        print(f"   Topics Progress: {len(student_data['progress']['topics_progress'])}")
    
    print(f"\n6. 🔄 SESSION FLOW SUMMARY")
    print("-" * 30)
    
    print("Complete Learning Session Flow:")
    print("1. 📚 Load integrated curriculum (IXL + Mavia)")
    print("2. 👤 Retrieve student profile and progress")
    print("3. 🎯 Analyze mastered topics and prerequisites")
    print("4. 🛤️  Select appropriate learning path")
    print("5. 📖 Choose next topic based on curriculum logic")
    print("6. 🎓 Conduct learning session with chosen topic")
    print("7. 📊 Update progress based on performance")
    print("8. 💾 Persist updated data to storage")
    print("9. 🔄 Repeat for next session")
    
    print(f"\n✨ Demo Complete!")
    print(f"The system now has a complete curriculum with {len(curriculum.topics)} topics")
    print(f"and detailed progress tracking for student {updated_student.profile.name}.")


if __name__ == "__main__":
    asyncio.run(demonstrate_curriculum_flow())
