#!/usr/bin/env python3
"""
Basic usage example for the Mavia Math Tutor system.

This example demonstrates how to:
1. Initialize the tutoring system
2. Create students with different profiles
3. Start tutoring sessions
4. Track progress and results
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.mavia_tutor.main import MaviaTutor


async def basic_usage_example():
    """Demonstrate basic usage of the Mavia Math Tutor system."""
    
    print("🎓 Mavia Math Tutor - Basic Usage Example")
    print("=" * 50)
    
    # Initialize the tutoring system
    print("1. Initializing Mavia Math Tutor...")
    tutor = MaviaTutor()
    print("   ✅ System initialized successfully!")
    
    # Create different types of students
    print("\n2. Creating student profiles...")
    
    # Beginner student
    alice = tutor.create_student(
        student_id="alice_001",
        name="<PERSON>",
        age=10,
        grade_level="5th",
        math_level="beginner",
        learning_style="visual",
        goals=["Learn basic arithmetic", "Understand fractions"],
        strengths=["Visual learning", "Pattern recognition"],
        challenges=["Word problems", "Remembering steps"]
    )
    print(f"   ✅ Created beginner student: {alice.profile.name}")
    
    # Elementary student
    bob = tutor.create_student(
        student_id="bob_002",
        name="Bob <PERSON>",
        age=12,
        grade_level="7th",
        math_level="elementary",
        learning_style="kinesthetic",
        goals=["Master fractions", "Learn basic algebra"],
        strengths=["Hands-on learning", "Logical thinking"],
        challenges=["Abstract concepts", "Sitting still"]
    )
    print(f"   ✅ Created elementary student: {bob.profile.name}")
    
    # Intermediate student
    carol = tutor.create_student(
        student_id="carol_003",
        name="Carol Davis",
        age=14,
        grade_level="9th",
        math_level="intermediate",
        learning_style="reading_writing",
        goals=["Advanced algebra", "Geometry proofs"],
        strengths=["Reading comprehension", "Note-taking"],
        challenges=["Visual-spatial tasks", "Time pressure"]
    )
    print(f"   ✅ Created intermediate student: {carol.profile.name}")
    
    # List all students
    print("\n3. Student roster:")
    students = tutor.list_students()
    for student in students:
        print(f"   • {student['name']} ({student['student_id']}) - Level: {student['math_level']}")
    
    # Start tutoring sessions for each student
    print("\n4. Starting tutoring sessions...")
    
    # Alice's session (beginner)
    print(f"\n   📚 Starting session for {alice.profile.name}...")
    alice_session = await tutor.start_tutoring_session(
        student_id="alice_001",
        session_goals=["Practice basic arithmetic", "Introduction to fractions"],
        max_duration_minutes=30
    )
    print(f"   ✅ Completed session: {alice_session}")
    
    # Bob's session (elementary)
    print(f"\n   📚 Starting session for {bob.profile.name}...")
    bob_session = await tutor.start_tutoring_session(
        student_id="bob_002",
        target_topics=["fractions_operations", "percentages"],
        session_goals=["Practice fraction operations", "Learn percentages"],
        max_duration_minutes=45
    )
    print(f"   ✅ Completed session: {bob_session}")
    
    # Carol's session (intermediate)
    print(f"\n   📚 Starting session for {carol.profile.name}...")
    carol_session = await tutor.start_tutoring_session(
        student_id="carol_003",
        target_topics=["algebra_basics", "linear_equations"],
        session_goals=["Review algebra basics", "Introduction to linear equations"],
        max_duration_minutes=60
    )
    print(f"   ✅ Completed session: {carol_session}")
    
    # Display session summaries
    print("\n5. Session summaries:")
    
    sessions = [
        ("Alice", alice_session),
        ("Bob", bob_session),
        ("Carol", carol_session)
    ]
    
    for name, session_id in sessions:
        summary = tutor.get_session_summary(session_id)
        if summary:
            print(f"\n   📊 {name}'s Session:")
            print(f"      Duration: {summary.get('duration_minutes', 0):.1f} minutes")
            print(f"      Topics completed: {summary.get('topics_completed', 0)}")
            print(f"      Assessments taken: {summary.get('assessments_taken', 0)}")
            print(f"      Status: {'Active' if summary.get('is_active', False) else 'Completed'}")
    
    # Display updated student progress
    print("\n6. Updated student progress:")
    
    for student_id, name in [("alice_001", "Alice"), ("bob_002", "Bob"), ("carol_003", "Carol")]:
        progress = tutor.get_student_summary(student_id)
        if progress:
            print(f"\n   📈 {name}'s Progress:")
            print(f"      Total sessions: {progress.get('total_sessions', 0)}")
            print(f"      Total time: {progress.get('total_time_hours', 0)} hours")
            print(f"      Topics mastered: {progress.get('topics_mastered', 0)}")
            print(f"      Achievements: {progress.get('achievements', 0)}")
    
    print("\n🎉 Basic usage example completed successfully!")
    print("\nNext steps:")
    print("• Try the CLI interface: python cli.py --help")
    print("• Explore advanced examples in the examples/ directory")
    print("• Check the documentation for customization options")


async def demonstrate_progress_tracking():
    """Demonstrate progress tracking capabilities."""
    
    print("\n" + "=" * 50)
    print("📊 Progress Tracking Demonstration")
    print("=" * 50)
    
    tutor = MaviaTutor()
    
    # Create a student for progress tracking
    student = tutor.create_student(
        student_id="progress_demo",
        name="Progress Demo Student",
        math_level="elementary",
        goals=["Master fractions"]
    )
    
    print(f"Created student: {student.profile.name}")
    
    # Simulate multiple sessions over time
    print("\nSimulating multiple learning sessions...")
    
    session_configs = [
        {
            "goals": ["Basic arithmetic review"],
            "duration": 20,
            "description": "Session 1: Arithmetic review"
        },
        {
            "goals": ["Introduction to fractions"],
            "duration": 30,
            "description": "Session 2: Fraction basics"
        },
        {
            "goals": ["Fraction operations"],
            "duration": 35,
            "description": "Session 3: Fraction operations"
        }
    ]
    
    for i, config in enumerate(session_configs, 1):
        print(f"\n   {config['description']}...")
        
        session_id = await tutor.start_tutoring_session(
            student_id="progress_demo",
            session_goals=config["goals"],
            max_duration_minutes=config["duration"]
        )
        
        # Simulate some progress
        tutor.student_manager.update_topic_progress(
            student_id="progress_demo",
            topic_id=f"topic_{i}",
            topic_name=f"Topic {i}",
            correct=True,
            time_spent=config["duration"]
        )
        
        print(f"   ✅ Completed {config['description']}")
    
    # Show final progress
    final_progress = tutor.get_student_summary("progress_demo")
    print(f"\n📈 Final Progress Summary:")
    print(f"   Total sessions: {final_progress.get('total_sessions', 0)}")
    print(f"   Total time: {final_progress.get('total_time_hours', 0)} hours")
    print(f"   Topics mastered: {final_progress.get('topics_mastered', 0)}")


async def demonstrate_different_learning_styles():
    """Demonstrate adaptation to different learning styles."""
    
    print("\n" + "=" * 50)
    print("🎨 Learning Style Adaptation Demonstration")
    print("=" * 50)
    
    tutor = MaviaTutor()
    
    learning_styles = [
        ("visual", "Visual learner - prefers diagrams and charts"),
        ("auditory", "Auditory learner - prefers verbal explanations"),
        ("kinesthetic", "Kinesthetic learner - prefers hands-on activities"),
        ("reading_writing", "Reading/Writing learner - prefers text and notes")
    ]
    
    for style, description in learning_styles:
        print(f"\n🎯 Creating {style} learner...")
        
        student = tutor.create_student(
            student_id=f"style_{style}",
            name=f"{style.title()} Learner",
            math_level="elementary",
            learning_style=style,
            goals=["Learn fractions"]
        )
        
        print(f"   Student: {student.profile.name}")
        print(f"   Style: {description}")
        
        # Start a short session to demonstrate adaptation
        session_id = await tutor.start_tutoring_session(
            student_id=f"style_{style}",
            session_goals=["Fraction introduction"],
            max_duration_minutes=15
        )
        
        print(f"   ✅ Completed adaptive session: {session_id}")
    
    print("\n💡 The system adapts teaching methods based on learning styles:")
    print("   • Visual: Uses diagrams, charts, and visual representations")
    print("   • Auditory: Emphasizes verbal explanations and patterns")
    print("   • Kinesthetic: Includes hands-on activities and manipulation")
    print("   • Reading/Writing: Provides written steps and note-taking")


if __name__ == "__main__":
    async def main():
        try:
            await basic_usage_example()
            await demonstrate_progress_tracking()
            await demonstrate_different_learning_styles()
            
        except KeyboardInterrupt:
            print("\n\n👋 Example interrupted by user")
        except Exception as e:
            print(f"\n❌ Error running example: {e}")
            import traceback
            traceback.print_exc()
    
    asyncio.run(main())
