#!/usr/bin/env python3
"""
Direct Agent Testing Demo with Real LLM APIs

This demo directly tests individual agents with real LLM APIs to validate
the multi-agent flow without the complex ADK session setup.
"""

import asyncio
import sys
import json
import os
from pathlib import Path
from datetime import datetime
import logging

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from mavia_tutor.main import MaviaTutor
from mavia_tutor.models.student import MathLevel, LearningStyle
from mavia_tutor.agents.learning_path_agent import LearningPathAgent
from mavia_tutor.agents.topic_explainer_agent import TopicExplainerAgent
from mavia_tutor.agents.testing_agent import TestingAgent

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_learning_path_agent(tutor: MaviaTutor, student_id: str):
    """Test the Learning Path Agent with real LLM APIs."""
    print("\n🛤️  Testing Learning Path Agent")
    print("-" * 40)
    
    try:
        student = tutor.get_student(student_id)
        curriculum = tutor.curriculum
        
        # Create learning path agent
        path_agent = LearningPathAgent(curriculum=curriculum)
        
        # Prepare student data for the agent
        student_data = {
            "student_id": student.profile.student_id,
            "name": student.profile.name,
            "age": student.profile.age,
            "grade_level": student.profile.grade_level,
            "math_level": student.profile.math_level.value,
            "learning_style": student.profile.learning_style.value,
            "goals": student.profile.goals,
            "strengths": student.profile.strengths,
            "challenges": student.profile.challenges
        }
        
        # Get current progress
        mastered_topics = set()
        for topic_id, progress in student.progress.topics_progress.items():
            if progress.mastery_level >= 0.8:
                mastered_topics.add(topic_id)
        
        current_progress = {
            "mastered_topics": list(mastered_topics),
            "total_sessions": student.progress.total_sessions,
            "total_time_minutes": student.progress.total_time_minutes
        }
        
        session_goals = ["Practice basic arithmetic", "Build confidence", "Prepare for next level"]
        
        print(f"📊 Input Data:")
        print(f"   Student: {student_data['name']} (Level: {student_data['math_level']})")
        print(f"   Mastered Topics: {len(mastered_topics)}")
        print(f"   Session Goals: {', '.join(session_goals)}")
        
        # Test the agent's path generation
        print(f"\n🤖 Calling Learning Path Agent...")
        
        # Create a simple prompt for the agent
        prompt = f"""
        Generate a personalized learning path for this student:
        
        Student Profile:
        - Name: {student_data['name']}
        - Age: {student_data['age']}
        - Grade: {student_data['grade_level']}
        - Math Level: {student_data['math_level']}
        - Learning Style: {student_data['learning_style']}
        - Goals: {', '.join(student_data['goals'])}
        - Strengths: {', '.join(student_data['strengths'])}
        - Challenges: {', '.join(student_data['challenges'])}
        
        Current Progress:
        - Mastered Topics: {len(mastered_topics)}
        - Total Sessions: {current_progress['total_sessions']}
        - Total Time: {current_progress['total_time_minutes']} minutes
        
        Session Goals: {', '.join(session_goals)}
        
        Available Topics in Curriculum: {len(curriculum.topics)}
        
        Please generate a learning path with 3-5 topics that would be appropriate for this student.
        Consider their level, goals, and current progress.
        """
        
        # Use the agent's generate method directly
        response = await path_agent.generate_learning_path(
            student_data=student_data,
            current_progress=current_progress,
            session_goals=session_goals
        )
        
        print(f"✅ Learning Path Generated:")
        if isinstance(response, dict):
            if 'topic_sequence' in response:
                for i, topic_id in enumerate(response['topic_sequence'], 1):
                    topic = curriculum.get_topic(topic_id)
                    topic_name = topic.name if topic else topic_id
                    print(f"   {i}. {topic_name}")
            if 'estimated_duration' in response:
                print(f"   Estimated Duration: {response['estimated_duration']:.1f} hours")
        else:
            print(f"   Response: {str(response)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Learning Path Agent failed: {str(e)}")
        logger.error("Learning Path Agent test failed", exc_info=True)
        return False


async def test_topic_explainer_agent(tutor: MaviaTutor, topic_id: str):
    """Test the Topic Explainer Agent with real LLM APIs."""
    print(f"\n📚 Testing Topic Explainer Agent")
    print("-" * 40)
    
    try:
        curriculum = tutor.curriculum
        topic = curriculum.get_topic(topic_id)
        
        if not topic:
            print(f"❌ Topic '{topic_id}' not found in curriculum")
            return False
        
        # Create topic explainer agent
        explainer_agent = TopicExplainerAgent()
        
        print(f"📖 Topic: {topic.name}")
        print(f"   Category: {topic.category.value}")
        print(f"   Difficulty: {topic.difficulty.value}")
        print(f"   Description: {topic.description[:100]}...")
        
        # Test explanation generation
        print(f"\n🤖 Calling Topic Explainer Agent...")
        
        student_context = {
            "age": 13,
            "grade_level": "Year 8",
            "math_level": "intermediate",
            "learning_style": "kinesthetic"
        }
        
        explanation = await explainer_agent.explain_topic(
            topic=topic,
            student_context=student_context
        )
        
        print(f"✅ Topic Explanation Generated:")
        print(f"   Length: {len(str(explanation))} characters")
        print(f"   Preview: {str(explanation)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Topic Explainer Agent failed: {str(e)}")
        logger.error("Topic Explainer Agent test failed", exc_info=True)
        return False


async def test_testing_agent(tutor: MaviaTutor, topic_id: str):
    """Test the Testing Agent with real LLM APIs."""
    print(f"\n🧪 Testing Testing Agent")
    print("-" * 40)
    
    try:
        curriculum = tutor.curriculum
        topic = curriculum.get_topic(topic_id)
        
        if not topic:
            print(f"❌ Topic '{topic_id}' not found in curriculum")
            return False
        
        # Create testing agent
        testing_agent = TestingAgent()
        
        print(f"📝 Topic: {topic.name}")
        print(f"   Difficulty: {topic.difficulty.value}")
        
        # Test question generation
        print(f"\n🤖 Calling Testing Agent...")
        
        student_context = {
            "age": 13,
            "grade_level": "Year 8",
            "math_level": "intermediate",
            "learning_style": "kinesthetic"
        }
        
        questions = await testing_agent.generate_questions(
            topic=topic,
            student_context=student_context,
            num_questions=3
        )
        
        print(f"✅ Questions Generated:")
        if isinstance(questions, list):
            for i, question in enumerate(questions, 1):
                print(f"   {i}. {str(question)[:100]}...")
        else:
            print(f"   Response: {str(questions)[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Testing Agent failed: {str(e)}")
        logger.error("Testing Agent test failed", exc_info=True)
        return False


async def simulate_learning_session(tutor: MaviaTutor, student_id: str, topic_id: str):
    """Simulate a complete learning session flow."""
    print(f"\n🎓 Simulating Learning Session")
    print("-" * 40)
    
    try:
        curriculum = tutor.curriculum
        topic = curriculum.get_topic(topic_id)
        student = tutor.get_student(student_id)
        
        if not topic or not student:
            print(f"❌ Topic or student not found")
            return False
        
        print(f"👤 Student: {student.profile.name}")
        print(f"📖 Topic: {topic.name}")
        
        # Step 1: Generate learning path
        print(f"\n1️⃣ Generating Learning Path...")
        path_agent = LearningPathAgent(curriculum=curriculum)
        
        student_data = {
            "student_id": student.profile.student_id,
            "name": student.profile.name,
            "math_level": student.profile.math_level.value,
            "learning_style": student.profile.learning_style.value,
            "goals": student.profile.goals
        }
        
        learning_path = await path_agent.generate_learning_path(
            student_data=student_data,
            current_progress={"mastered_topics": []},
            session_goals=[f"Learn {topic.name}"]
        )
        print(f"✅ Learning path generated")
        
        # Step 2: Assess current knowledge
        print(f"\n2️⃣ Assessing Current Knowledge...")
        testing_agent = TestingAgent()
        
        assessment = await testing_agent.assess_knowledge(
            topic=topic,
            student_context={"math_level": student.profile.math_level.value}
        )
        print(f"✅ Assessment completed")
        
        # Step 3: Provide explanation
        print(f"\n3️⃣ Providing Topic Explanation...")
        explainer_agent = TopicExplainerAgent()
        
        explanation = await explainer_agent.explain_topic(
            topic=topic,
            student_context={"learning_style": student.profile.learning_style.value}
        )
        print(f"✅ Explanation provided")
        
        # Step 4: Update progress
        print(f"\n4️⃣ Updating Progress...")
        success = tutor.student_manager.update_topic_progress(
            student_id=student_id,
            topic_id=topic_id,
            topic_name=topic.name,
            correct=True,  # Simulate successful learning
            time_spent=25.0  # 25 minutes
        )
        
        if success:
            print(f"✅ Progress updated successfully")
        else:
            print(f"❌ Failed to update progress")
        
        print(f"\n🎉 Learning session simulation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Learning session simulation failed: {str(e)}")
        logger.error("Learning session simulation failed", exc_info=True)
        return False


async def main():
    """Run the direct agent testing demo."""
    print("🚀 Mavia Math Tutor - Direct Agent Testing Demo")
    print("=" * 60)
    print("Testing individual agents with real LLM APIs")
    print()
    
    # Check environment
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found in environment")
        print("Please set your Gemini API key in the .env file")
        return
    
    try:
        # Initialize tutor
        print("🔧 Initializing Mavia Math Tutor...")
        tutor = MaviaTutor()
        print("✅ Tutor initialized successfully")
        
        # Create demo student
        student_id = "demo_student_direct_001"
        student = tutor.create_student(
            student_id=student_id,
            name="Alex Chen",
            age=13,
            grade_level="Year 8",
            math_level=MathLevel.INTERMEDIATE,
            learning_style=LearningStyle.VISUAL,
            goals=["Master basic arithmetic", "Improve problem-solving"],
            strengths=["Visual learning", "Pattern recognition"],
            challenges=["Word problems", "Complex calculations"]
        )
        
        print(f"✅ Created student: {student.profile.name}")
        
        # Test individual agents
        test_results = []
        
        # Test Learning Path Agent
        result1 = await test_learning_path_agent(tutor, student_id)
        test_results.append(("Learning Path Agent", result1))
        
        # Test Topic Explainer Agent
        result2 = await test_topic_explainer_agent(tutor, "basic_arithmetic")
        test_results.append(("Topic Explainer Agent", result2))
        
        # Test Testing Agent
        result3 = await test_testing_agent(tutor, "basic_arithmetic")
        test_results.append(("Testing Agent", result3))
        
        # Simulate complete learning session
        result4 = await simulate_learning_session(tutor, student_id, "basic_arithmetic")
        test_results.append(("Learning Session Simulation", result4))
        
        # Summary
        print(f"\n🎉 Demo Complete!")
        print("-" * 40)
        successful_tests = sum(1 for _, result in test_results if result)
        print(f"✅ Successful tests: {successful_tests}/{len(test_results)}")
        
        print(f"\n🔍 Test Results:")
        for test_name, result in test_results:
            status = "✅" if result else "❌"
            print(f"   {status} {test_name}")
        
        # Show final student progress
        updated_student = tutor.get_student(student_id)
        if updated_student and updated_student.progress.topics_progress:
            print(f"\n📊 Final Student Progress:")
            for topic_id, progress in updated_student.progress.topics_progress.items():
                mastery_percent = progress.mastery_level * 100
                print(f"   • {progress.topic_name}: {mastery_percent:.1f}% mastery")
        
        print(f"\n💾 Student data saved to: data/students/{student_id}.json")
        
    except Exception as e:
        print(f"❌ Demo failed: {str(e)}")
        logger.error("Demo failed", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
