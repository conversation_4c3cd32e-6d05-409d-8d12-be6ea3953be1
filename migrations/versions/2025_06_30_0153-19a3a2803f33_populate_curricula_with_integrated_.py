"""populate_curricula_with_integrated_curriculum

Revision ID: 19a3a2803f33
Revises:
Create Date: 2025-06-30 01:53:01.428006

"""
from alembic import op
import sqlalchemy as sa
import json
import os
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = '19a3a2803f33'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Populate curricula table with integrated curriculum data."""

    # Get the path to the integrated curriculum file
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
    curriculum_file = os.path.join(project_root, 'data', 'integrated_curriculum', 'integrated_curriculum.json')

    if not os.path.exists(curriculum_file):
        print(f"Warning: Integrated curriculum file not found at {curriculum_file}")
        return

    # Load the integrated curriculum data
    with open(curriculum_file, 'r', encoding='utf-8') as f:
        curriculum_data = json.load(f)

    # Get database connection
    connection = op.get_bind()

    # Insert curriculum record
    curriculum_insert = text("""
        INSERT INTO curricula (curriculum_id, name, description, version, is_active, curriculum_metadata, created_at, updated_at)
        VALUES (:curriculum_id, :name, :description, :version, :is_active, :curriculum_metadata, NOW(), NOW())
        ON CONFLICT (curriculum_id) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            version = EXCLUDED.version,
            curriculum_metadata = EXCLUDED.curriculum_metadata,
            updated_at = NOW()
        RETURNING id
    """)

    curriculum_result = connection.execute(curriculum_insert, {
        'curriculum_id': curriculum_data['curriculum_id'],
        'name': curriculum_data['name'],
        'description': curriculum_data['description'],
        'version': curriculum_data.get('version', '1.0'),
        'is_active': True,
        'curriculum_metadata': json.dumps({
            'source': 'integrated_curriculum',
            'topics_count': len(curriculum_data['topics']),
            'learning_paths_count': len(curriculum_data.get('learning_paths', {}))
        })
    })

    # Get the curriculum ID
    curriculum_id = curriculum_result.fetchone()[0]

    # Insert topics
    topics_data = curriculum_data.get('topics', {})
    for topic_id, topic_info in topics_data.items():
        topic_insert = text("""
            INSERT INTO topics (
                curriculum_id, topic_id, name, description, category, difficulty,
                prerequisites, learning_objectives, key_concepts, examples,
                estimated_time_minutes, content, created_at, updated_at
            )
            VALUES (
                :curriculum_id, :topic_id, :name, :description, :category, :difficulty,
                :prerequisites, :learning_objectives, :key_concepts, :examples,
                :estimated_time_minutes, :content, NOW(), NOW()
            )
        """)

        connection.execute(topic_insert, {
            'curriculum_id': curriculum_id,
            'topic_id': topic_info['topic_id'],
            'name': topic_info['name'],
            'description': topic_info.get('description', ''),
            'category': topic_info.get('category', 'general'),
            'difficulty': topic_info.get('difficulty', 'medium'),
            'prerequisites': topic_info.get('prerequisites', []),
            'learning_objectives': topic_info.get('learning_objectives', []),
            'key_concepts': topic_info.get('key_concepts', []),
            'examples': topic_info.get('examples', []),
            'estimated_time_minutes': topic_info.get('estimated_time_minutes', 60),
            'content': json.dumps(topic_info)
        })

    # Insert learning paths
    learning_paths_data = curriculum_data.get('learning_paths', {})
    for path_id, path_info in learning_paths_data.items():
        path_insert = text("""
            INSERT INTO learning_paths (
                curriculum_id, path_id, name, description, target_level,
                topic_sequence, estimated_duration_hours, prerequisites,
                path_metadata, created_at, updated_at
            )
            VALUES (
                :curriculum_id, :path_id, :name, :description, :target_level,
                :topic_sequence, :estimated_duration_hours, :prerequisites,
                :path_metadata, NOW(), NOW()
            )
        """)

        connection.execute(path_insert, {
            'curriculum_id': curriculum_id,
            'path_id': path_info['path_id'],
            'name': path_info['name'],
            'description': path_info.get('description', ''),
            'target_level': path_info.get('target_level', 'beginner'),
            'topic_sequence': path_info.get('topic_sequence', []),
            'estimated_duration_hours': path_info.get('estimated_duration_hours', 1),
            'prerequisites': path_info.get('prerequisites', []),
            'path_metadata': json.dumps(path_info)
        })

    print(f"Successfully populated curriculum '{curriculum_data['name']}' with {len(topics_data)} topics and {len(learning_paths_data)} learning paths")


def downgrade() -> None:
    """Remove the integrated curriculum data."""
    connection = op.get_bind()

    # Remove the integrated curriculum and all related data
    connection.execute(text("""
        DELETE FROM learning_paths WHERE curriculum_id IN (
            SELECT id FROM curricula WHERE curriculum_id = 'mavia_integrated_curriculum'
        )
    """))

    connection.execute(text("""
        DELETE FROM topics WHERE curriculum_id IN (
            SELECT id FROM curricula WHERE curriculum_id = 'mavia_integrated_curriculum'
        )
    """))

    connection.execute(text("""
        DELETE FROM curricula WHERE curriculum_id = 'mavia_integrated_curriculum'
    """))

    print("Removed integrated curriculum data")
