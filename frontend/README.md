# Agentic AI Math Tutor - Frontend

The frontend for the Agentic AI Math Tutor project, built with Svelte and powered by SvelteKit.

## Prerequisites

- [Bun](https://bun.sh/) (recommended) or Node.js
- Access to the backend API (see backend README for setup instructions)

## Setup

1. Clone the repository
2. Navigate to the frontend directory
3. Install dependencies:

```bash
# Using Bun (recommended)
bun install

# Or using npm/yarn/pnpm
npm install
# or
yarn
# or
pnpm install
```

4. Copy the `.env.example` file to `.env` and update the variables as needed:

```bash
cp .env.example .env
```

## Developing

Start a development server:

```bash
# Using Bun (recommended)
bun run dev

# Or using npm
npm run dev

# To open the app in a new browser tab, add --open
bun run dev --open
```

## Building

To create a production version of the app:

```bash
# Using Bun (recommended)
bun run build

# Or using npm
npm run build
```

## Previewing

Preview the production build:

```bash
# Using Bun (recommended)
bun run preview

# Or using npm
npm run preview
```

## Deployment

The project is configured for deployment on Netlify using the `netlify.toml` configuration file.

For other environments, you may need to install an [adapter](https://svelte.dev/docs/kit/adapters) for your target environment.

## Project Structure

```
/frontend
  /src
    /routes            # Page routes
      /app             # Application pages
        /learn         # Learning experience
        /practice      # Practice problems
        /test          # Test preparation
    /lib
      /components      # Reusable UI components
        /math          # Math-specific components
        /tutor         # Tutoring interface components
      /stores          # State management
      /utils           # Utility functions
      /api             # API client for backend communication
```

## Features

- Interactive math problem-solving interface
- Real-time feedback during problem-solving
- Step-by-step solution viewer
- Interactive learning path visualization
- Test preparation dashboard
- LaTeX support for mathematical expressions
