<script lang="ts">
	import '../app.css';
	import type { Snippet } from 'svelte';
	import { Clerk<PERSON>rovider } from 'svelte-clerk/client';
	import { PUBLIC_CLERK_PUBLISHABLE_KEY } from '$env/static/public';
	import ThemeProvider from '$lib/components/ThemeProvider.svelte';

	const { children }: { children: Snippet } = $props();
</script>

<svelte:head>
	<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
</svelte:head>
<div class="flex h-screen bg-background text-foreground relative">
	<div class="bg absolute inset-0 z-0 opacity-60"></div>
	<div class="bg bg2 absolute inset-0 z-0 opacity-60"></div>
	<div class="bg bg3 absolute inset-0 z-0 opacity-60"></div>
	
	<ClerkProvider publishableKey={PUBLIC_CLERK_PUBLISHABLE_KEY}>
		<ThemeProvider>
			{@render children()}
		</ThemeProvider>
	</ClerkProvider>
</div>
