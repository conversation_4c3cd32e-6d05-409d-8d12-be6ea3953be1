<script lang="ts">
  import { page } from '$app/stores';
  import { UserButton } from 'svelte-clerk';
  
  // Get user information
  $: firstName = $page.data.user?.firstName || "there";
</script>

<svelte:head>
  <!-- Google Fonts - Great Vibes -->
  <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap" rel="stylesheet">
</svelte:head>

<!-- Animated background inspired by page.tsx -->
<div class="body relative overflow-visible">
  <div class="bg absolute inset-0"></div>
  <div class="bg bg2 absolute inset-0"></div>
  <div class="bg bg3 absolute inset-0"></div>
  
  <div class="flex min-h-screen flex-col items-center justify-between p-4 md:p-0 relative overflow-y-auto overflow-x-hidden md:overflow-auto h-screen overscroll-contain">
    <!-- Logo link at top left -->
    <a href="/home.html">
      <img 
        src="/mavialogo.png" 
        alt="Mavia Logo" 
        style="position: fixed; height: auto; left: 30px; top: 30px;" 
        width="40"
      />
    </a>
    
    <!-- User button at bottom left -->
    <div class="absolute bottom-4 left-4 z-10" style="left: 30px;">
      <UserButton 
        appearance={{
          elements: {
            userButtonPopoverCard: "scale-90 origin-bottom-left",
            userButtonPopoverFooter: "hidden",
            userButtonPopoverActionButton: "py-1"
          }
        }}
      />
    </div>
    
    <!-- Spacer to push content to center -->
    <div class="flex-grow"></div>
    
    <!-- Main content - vertically centered -->
    <div class="p-4 md:p-8 flex items-center justify-center w-full my-auto">
      <div class="max-w-4xl mx-auto">
        <div class="flex items-center gap-x-10">
          <!-- Tutor avatar -->
          <div class="flex-shrink-0 ml-[5px]">
            <img 
              src="/homepageavatar.png" 
              alt="Avatar" 
              width="240" 
              height="240"
              class="object-cover"
              style="transform: rotateY(180deg);"
            />
          </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 mt-[-22]">
          <div class="group p-4 bg-card hover:bg-card/80 rounded-lg shadow-sm border border-border transition-colors w-xs">
            <div class="flex flex-col h-full">
              <div class="flex items-start justify-between mb-2">
                <h3 class="text-base font-medium">Hello {firstName}</h3>
              </div>
              <p class="text-muted-foreground text-xs flex-1">
                Thank you for signing up. I am excited to be your tutor. <br><br>Unfortunately, our waitlist is currently quite long. I am quite overwhelmed by all the interest! <br><br> But don't you worry. We have your email and I will reach out to you as soon as we are ready to accommodate you. <br><br> Looking forward to seeing you soon!
              </p>
              <p style="margin-top: 19px; font-size: 24px; font-family: 'Great Vibes', cursive;">
                Mavia
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
          
    <div class="container mx-auto text-center mt-auto">
      <p class="text-gray-400 text-xs font-light mx-auto">
        © {new Date().getFullYear()} Hyperacuity.ai
      </p>
    </div>
    
    <!-- Spacer to push content to center -->
    <div class="flex-grow"></div>
  </div>
</div>

<!-- No style tag needed - using global styles from app.css -->
