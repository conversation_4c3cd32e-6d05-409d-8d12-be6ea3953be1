<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { apiClient } from '$lib/api';
  import type { StudentResponse, StudentCreateRequest } from '$lib/api';
  
  // Get user info from Clerk
  $: user = $page.data.user;
  $: userId = $page.data.userId;
  
  // Student data
  let student: StudentResponse | null = null;
  let loading = true;
  let error = '';
  let isEditing = false;
  
  // Form data for creating/editing student
  let formData: StudentCreateRequest = {
    student_id: '',
    name: '',
    age: 15,
    grade_level: '9th',
    math_level: 'intermediate',
    learning_style: 'visual',
    goals: [],
    strengths: [],
    challenges: []
  };
  
  // Form inputs
  let goalInput = '';
  let strengthInput = '';
  let challengeInput = '';
  
  // Load student data on mount
  onMount(async () => {
    if (userId) {
      await loadStudentData();
    }
  });
  
  async function loadStudentData() {
    try {
      loading = true;
      error = '';
      
      // Try to get student by user ID (we'll use userId as student_id for now)
      const studentId = `student_${userId}`;
      student = await apiClient.getStudentDB(studentId);
      
      // Populate form with existing data
      if (student) {
        formData = {
          student_id: student.student_id,
          name: student.name,
          age: student.age,
          grade_level: student.grade_level,
          math_level: student.math_level,
          learning_style: student.learning_style || 'visual',
          goals: [...student.goals],
          strengths: [...student.strengths],
          challenges: [...student.challenges]
        };
      }
    } catch (err: any) {
      if (err.message.includes('404')) {
        // Student doesn't exist, prepare for creation
        student = null;
        formData.student_id = `student_${userId}`;
        formData.name = user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : '';
      } else {
        error = `Failed to load student data: ${err.message}`;
      }
    } finally {
      loading = false;
    }
  }
  
  async function saveStudent() {
    try {
      loading = true;
      error = '';
      
      if (student) {
        // Update existing student (we'll need to implement this endpoint)
        console.log('Update not implemented yet, creating new for now');
        student = await apiClient.createStudentDB(formData);
      } else {
        // Create new student
        student = await apiClient.createStudentDB(formData);
      }
      
      isEditing = false;
    } catch (err: any) {
      error = `Failed to save student: ${err.message}`;
    } finally {
      loading = false;
    }
  }
  
  function addGoal() {
    if (goalInput.trim() && formData.goals.length < 5) {
      formData.goals = [...formData.goals, goalInput.trim()];
      goalInput = '';
    }
  }
  
  function removeGoal(index: number) {
    formData.goals = formData.goals.filter((_, i) => i !== index);
  }
  
  function addStrength() {
    if (strengthInput.trim() && formData.strengths.length < 5) {
      formData.strengths = [...formData.strengths, strengthInput.trim()];
      strengthInput = '';
    }
  }
  
  function removeStrength(index: number) {
    formData.strengths = formData.strengths.filter((_, i) => i !== index);
  }
  
  function addChallenge() {
    if (challengeInput.trim() && formData.challenges.length < 5) {
      formData.challenges = [...formData.challenges, challengeInput.trim()];
      challengeInput = '';
    }
  }
  
  function removeChallenge(index: number) {
    formData.challenges = formData.challenges.filter((_, i) => i !== index);
  }
</script>

<div class="max-w-4xl mx-auto p-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">Student Profile</h1>
    {#if student && !isEditing}
      <button class="btn btn-primary" on:click={() => isEditing = true}>
        Edit Profile
      </button>
    {/if}
  </div>
  
  {#if loading}
    <div class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  {:else if error}
    <div class="alert alert-error">
      <span>{error}</span>
      <button class="btn btn-sm" on:click={loadStudentData}>Retry</button>
    </div>
  {:else if !student || isEditing}
    <!-- Create/Edit Form -->
    <div class="card bg-base-200 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">{student ? 'Edit' : 'Create'} Student Profile</h2>
        
        <form on:submit|preventDefault={saveStudent} class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-control">
              <label class="label" for="name">
                <span class="label-text">Full Name</span>
              </label>
              <input 
                id="name"
                type="text" 
                class="input input-bordered" 
                bind:value={formData.name}
                required 
              />
            </div>
            
            <div class="form-control">
              <label class="label" for="age">
                <span class="label-text">Age</span>
              </label>
              <input 
                id="age"
                type="number" 
                class="input input-bordered" 
                bind:value={formData.age}
                min="5" 
                max="25"
                required 
              />
            </div>
            
            <div class="form-control">
              <label class="label" for="grade">
                <span class="label-text">Grade Level</span>
              </label>
              <select id="grade" class="select select-bordered" bind:value={formData.grade_level}>
                <option value="5th">5th Grade</option>
                <option value="6th">6th Grade</option>
                <option value="7th">7th Grade</option>
                <option value="8th">8th Grade</option>
                <option value="9th">9th Grade</option>
                <option value="10th">10th Grade</option>
                <option value="11th">11th Grade</option>
                <option value="12th">12th Grade</option>
                <option value="college">College</option>
              </select>
            </div>
            
            <div class="form-control">
              <label class="label" for="math-level">
                <span class="label-text">Math Level</span>
              </label>
              <select id="math-level" class="select select-bordered" bind:value={formData.math_level}>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
                <option value="expert">Expert</option>
              </select>
            </div>
            
            <div class="form-control md:col-span-2">
              <label class="label" for="learning-style">
                <span class="label-text">Learning Style</span>
              </label>
              <select id="learning-style" class="select select-bordered" bind:value={formData.learning_style}>
                <option value="visual">Visual</option>
                <option value="auditory">Auditory</option>
                <option value="kinesthetic">Kinesthetic</option>
                <option value="reading">Reading/Writing</option>
              </select>
            </div>
          </div>
          
          <!-- Goals -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Learning Goals</span>
            </label>
            <div class="flex gap-2 mb-2">
              <input 
                type="text" 
                class="input input-bordered flex-1" 
                placeholder="Add a learning goal..."
                bind:value={goalInput}
                on:keydown={(e) => e.key === 'Enter' && addGoal()}
              />
              <button type="button" class="btn btn-primary" on:click={addGoal}>Add</button>
            </div>
            <div class="flex flex-wrap gap-2">
              {#each formData.goals as goal, index}
                <div class="badge badge-primary gap-2">
                  {goal}
                  <button type="button" class="btn btn-xs btn-circle" on:click={() => removeGoal(index)}>×</button>
                </div>
              {/each}
            </div>
          </div>
          
          <!-- Strengths -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Strengths</span>
            </label>
            <div class="flex gap-2 mb-2">
              <input 
                type="text" 
                class="input input-bordered flex-1" 
                placeholder="Add a strength..."
                bind:value={strengthInput}
                on:keydown={(e) => e.key === 'Enter' && addStrength()}
              />
              <button type="button" class="btn btn-secondary" on:click={addStrength}>Add</button>
            </div>
            <div class="flex flex-wrap gap-2">
              {#each formData.strengths as strength, index}
                <div class="badge badge-secondary gap-2">
                  {strength}
                  <button type="button" class="btn btn-xs btn-circle" on:click={() => removeStrength(index)}>×</button>
                </div>
              {/each}
            </div>
          </div>
          
          <!-- Challenges -->
          <div class="form-control">
            <label class="label">
              <span class="label-text">Challenges</span>
            </label>
            <div class="flex gap-2 mb-2">
              <input 
                type="text" 
                class="input input-bordered flex-1" 
                placeholder="Add a challenge..."
                bind:value={challengeInput}
                on:keydown={(e) => e.key === 'Enter' && addChallenge()}
              />
              <button type="button" class="btn btn-accent" on:click={addChallenge}>Add</button>
            </div>
            <div class="flex flex-wrap gap-2">
              {#each formData.challenges as challenge, index}
                <div class="badge badge-accent gap-2">
                  {challenge}
                  <button type="button" class="btn btn-xs btn-circle" on:click={() => removeChallenge(index)}>×</button>
                </div>
              {/each}
            </div>
          </div>
          
          <!-- Form Actions -->
          <div class="card-actions justify-end">
            {#if isEditing}
              <button type="button" class="btn btn-ghost" on:click={() => isEditing = false}>
                Cancel
              </button>
            {/if}
            <button type="submit" class="btn btn-primary" disabled={loading}>
              {loading ? 'Saving...' : student ? 'Update Profile' : 'Create Profile'}
            </button>
          </div>
        </form>
      </div>
    </div>
  {:else}
    <!-- Display Profile -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Basic Info Card -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Basic Information</h2>
          <div class="space-y-2">
            <p><strong>Name:</strong> {student.name}</p>
            <p><strong>Age:</strong> {student.age}</p>
            <p><strong>Grade:</strong> {student.grade_level}</p>
            <p><strong>Math Level:</strong> {student.math_level}</p>
            <p><strong>Learning Style:</strong> {student.learning_style || 'Not specified'}</p>
            <p><strong>Email:</strong> {student.user_email}</p>
            <p><strong>Subscription:</strong> 
              <span class="badge badge-{student.subscription_status === 'active' ? 'success' : 'warning'}">
                {student.subscription_status}
              </span>
            </p>
          </div>
        </div>
      </div>
      
      <!-- Goals, Strengths, Challenges Card -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Learning Profile</h2>
          
          <div class="space-y-4">
            <div>
              <h3 class="font-semibold mb-2">Goals</h3>
              <div class="flex flex-wrap gap-2">
                {#each student.goals as goal}
                  <div class="badge badge-primary">{goal}</div>
                {:else}
                  <p class="text-gray-500">No goals set</p>
                {/each}
              </div>
            </div>
            
            <div>
              <h3 class="font-semibold mb-2">Strengths</h3>
              <div class="flex flex-wrap gap-2">
                {#each student.strengths as strength}
                  <div class="badge badge-secondary">{strength}</div>
                {:else}
                  <p class="text-gray-500">No strengths listed</p>
                {/each}
              </div>
            </div>
            
            <div>
              <h3 class="font-semibold mb-2">Challenges</h3>
              <div class="flex flex-wrap gap-2">
                {#each student.challenges as challenge}
                  <div class="badge badge-accent">{challenge}</div>
                {:else}
                  <p class="text-gray-500">No challenges listed</p>
                {/each}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
