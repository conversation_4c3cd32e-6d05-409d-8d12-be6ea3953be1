<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { apiClient } from '$lib/api';
  import type { SessionResponse } from '$lib/api';

  // Mock data and functions to replace useChat
  let messages: {
    id: string;
    role: 'user' | 'assistant';
    parts: { type: string; text: string }[];
  }[] = [];

  let input = '';
  let isLoading = false;
  let currentSession: SessionResponse | null = null;

  // Get user data
  $: user = $page.data.user;
  $: userId = $page.data.userId;
  $: userName = user?.firstName || 'User';

  // Generate user initials from name
  const getUserInitials = () => {
    return userName
      .split(" ")
      .map((name: string) => name?.[0] || "")
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Create a new learning session
  async function createSession() {
    if (!userId) return;

    try {
      const sessionId = `learn_${userId}_${Date.now()}`;
      const studentId = `student_${userId}`;

      // Get student name from database first
      let studentName = userName;
      try {
        const student = await apiClient.getStudentDB(studentId);
        if (student && student.name) {
          studentName = student.name.split(' ')[0]; // Use first name only
        }
      } catch (error) {
        console.log('Could not fetch student name, using fallback:', studentName);
      }

      currentSession = await apiClient.createSessionDB({
        session_id: sessionId,
        student_id: studentId,
        target_topics: ['general_learning'],
        session_goals: ['Interactive learning session'],
        max_duration_minutes: 60
      });

      console.log('New learning session created:', currentSession);

      // Clear any existing conversation history to start fresh
      await clearConversationHistory();

      // Add welcome message for new session with actual student name
      const welcomeText = `Hi ${studentName}! 👋 Welcome to your new learning session! I'm here to help you learn and practice math. What would you like to work on today?`;
      const welcomeMessage = {
        id: `welcome_${Date.now()}`,
        role: 'assistant' as const,
        parts: [{
          type: 'text',
          text: welcomeText
        }]
      };
      messages = [welcomeMessage];

      // Save welcome message to database
      try {
        await apiClient.addChatMessageDB({
          session_id: currentSession.session_id,
          role: 'assistant',
          content: welcomeText,
          message_type: 'text',
          message_id: welcomeMessage.id
        });
      } catch (dbError) {
        console.error('Failed to save welcome message to database:', dbError);
      }

      // Trigger session list refresh in sidebar
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('sessionCreated', {
          detail: { sessionId: currentSession.session_id }
        }));
      }

    } catch (error) {
      console.error('Failed to create session:', error);
    }
  }

  // Clear conversation history for fresh start
  async function clearConversationHistory() {
    try {
      const studentId = userId ? `student_${userId}` : 'default_student';
      await fetch(`/api/sessions/clear/${studentId}?context=all`, { method: 'POST' });

      // Clear local messages
      messages = [];

      console.log('Conversation history cleared for new session');
    } catch (error) {
      console.error('Failed to clear conversation history:', error);
    }
  }

  // Function to load existing session
  async function loadExistingSession(sessionId: string) {
    try {
      // Get session details
      currentSession = await apiClient.getSessionDB(sessionId);
      console.log('Loaded existing session:', currentSession);

      // Load messages for this session
      const messagesData = await apiClient.getSessionMessagesDB(sessionId);

      // Convert database messages to frontend format
      messages = messagesData.messages.map(msg => ({
        id: msg.message_id || msg.id,
        role: msg.role as 'user' | 'assistant' | 'system',
        parts: [{ type: 'text', text: msg.content }]
      }));

      console.log(`Loaded ${messages.length} messages for session ${sessionId}`);

    } catch (error) {
      console.error('Failed to load existing session:', error);
      // Fall back to creating new session
      await createSession();
    }
  }

  // Initialize session on mount and every time page is visited
  onMount(async () => {
    // Check if there's a session ID in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session');

    if (sessionId) {
      await loadExistingSession(sessionId);
    } else {
      await createSession();
    }
  });

  // Function to handle input changes
  function handleInputChange(event: Event) {
    input = (event.target as HTMLInputElement).value;
  }



  // Function to handle submission
  async function handleSubmit(event: Event) {
    event.preventDefault();

    if (!input.trim() || isLoading || !currentSession) return;

    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      parts: [{ type: 'text', text: input.trim() }]
    };

    messages = [...messages, userMessage];

    // Clear input and set loading
    const userInput = input;
    input = '';
    isLoading = true;

    try {
      // Use the API client to send learn message
      const studentId = userId ? `student_${userId}` : null;
      if (!studentId) {
        throw new Error('User ID not available. Please log in.');
      }

      // Save user message to database
      await apiClient.addChatMessageDB({
        session_id: currentSession.session_id,
        role: 'user',
        content: userInput,
        message_type: 'text',
        message_id: userMessage.id
      });

      const response = await apiClient.sendLearnMessage({
        message: userInput,
        student_id: studentId
      });

      // Add assistant message
      const assistantMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        parts: [{ type: 'text', text: response.text || "Let's explore this concept together!" }]
      };

      messages = [...messages, assistantMessage];

      // Save assistant message to database
      await apiClient.addChatMessageDB({
        session_id: currentSession.session_id,
        role: 'assistant',
        content: response.text || "Let's explore this concept together!",
        message_type: 'text',
        message_id: assistantMessage.id
      });

    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant' as const,
        parts: [{ type: 'text', text: "Sorry, I couldn't process your request at the moment." }]
      };

      messages = [...messages, errorMessage];

      // Try to save error message to database (optional)
      try {
        if (currentSession) {
          await apiClient.addChatMessageDB({
            session_id: currentSession.session_id,
            role: 'assistant',
            content: "Sorry, I couldn't process your request at the moment.",
            message_type: 'text',
            message_id: errorMessage.id
          });
        }
      } catch (dbError) {
        console.error('Failed to save error message to database:', dbError);
      }
    } finally {
      isLoading = false;
    }
  }

  // Scroll to bottom when messages change
  $: if (messages.length > 0) {
    setTimeout(() => {
      const chatContainer = document.getElementById("practice-messages-container");
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }, 0);
  }

  // Ensure we scroll on mount too
  onMount(() => {
    const chatContainer = document.getElementById("practice-messages-container");
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  });
</script>

<div class="flex flex-col h-full bg-transparent">
  <!-- Messages area -->
  <div
    id="practice-messages-container"
    class="flex-1 overflow-y-auto py-4 space-y-4 px-4 sm:px-6 md:px-32"
  >
    {#if messages.length === 0}
      <div class="flex items-center justify-center h-full relative">
        <div class="text-center relative z-10">
          <img
            src="/mavialogo.png"
            alt="Mavia"
            width="64"
            height="64"
            class="mx-auto mb-4 relative z-10"
          />
          <p class="text-foreground font-medium relative z-10">
            I'll help you learn new concepts step by step. What would you like to explore?
          </p>
        </div>
      </div>
    {:else}
      {#each messages as message (message.id)}
        <div class="w-full relative mb-4">
          {#if message.role === "user"}
            <!-- User message - left aligned with avatar first -->
            <div class="w-full flex justify-start">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-semibold">
                  {getUserInitials()}
                </div>
                <div class="rounded-lg py-2 px-4 ml-0">
                  {#each message.parts as part}
                    {#if part.type === 'text'}
                      <p>{part.text}</p>
                    {/if}
                  {/each}
                </div>
              </div>
            </div>
          {:else}
            <!-- Assistant message - left aligned -->
            <div class="w-full flex justify-start">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center">
                  <img
                    src="/mavialogo.png"
                    alt="Mavia"
                    width="40"
                    height="40"
                    class="object-contain"
                  />
                </div>
                <div class="rounded-lg py-2 px-4 ml-0">
                  {#each message.parts as part}
                    {#if part.type === 'text'}
                      <p>{part.text}</p>
                    {/if}
                  {/each}
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/each}
    {/if}

    {#if isLoading}
      <div class="w-full relative mb-8">
        <div class="w-full flex justify-start">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 flex items-center justify-center">
              <img
                src="/mavialogo.png"
                alt="Mavia"
                width="40"
                height="40"
                class="object-contain"
              />
            </div>
            <div class="rounded-lg py-2 px-4 ml-0">
              <div class="flex space-x-2 items-center">
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce"></div>
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style="animation-delay:0.2s"></div>
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style="animation-delay:0.4s"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Input area -->
  <div class="sticky bottom-0 left-0 right-0 bg-transparent pt-2 pb-0 px-4 sm:px-6 md:px-32">
    <form on:submit={handleSubmit} class="relative">
      <input
        type="text"
        bind:value={input}
        on:input={handleInputChange}
        placeholder="Type your answer or question..."
        class="w-full p-4.5 md:p-3 pr-10 rounded-full bg-card border border-border focus:outline-none focus:ring-2 focus:ring-primary/50 text-sm"
        disabled={isLoading}
      />
      <button
        type="submit"
        class="absolute right-2 top-1/2 -translate-y-1/2 w-7 h-7 md:w-8 md:h-8 flex items-center justify-center rounded-full bg-primary text-primary-foreground disabled:opacity-50"
        disabled={!input.trim() || isLoading}
        aria-label="Send message"
      >
        <!-- SVG for Send icon instead of importing from lucide-react -->
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5 md:h-4 md:w-4">
          <path d="m22 2-7 20-4-9-9-4Z"></path>
          <path d="M22 2 11 13"></path>
        </svg>
      </button>
    </form>
  </div>
</div>
