<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { get } from 'svelte/store';
  import { liveAPI } from '$lib/stores/live-api';
  import { mediaManager } from '$lib/stores/media-manager';
  import ControlTray from '$lib/components/live-chat/ControlTray.svelte';
  import VideoDisplay from '$lib/components/live-chat/VideoDisplay.svelte';
  import type { ConversationMessage } from '$lib/live-chat/types';
  
  // Media state for showing/hiding video container
  let hasActiveMedia = false;
  
  // Subscribe to media state to determine if videos are active
  const mediaUnsubscribe = mediaManager.subscribe(state => {
    hasActiveMedia = state.webcam.active || state.screenShare.active;
  });
  
  // Messages array
  let messages: {
    id: string;
    role: 'user' | 'assistant';
    parts: { type: string; text: string }[];
  }[] = [];
  
  let input = '';
  let isLoading = false;
  let modelMessage = '';
  
  // User name prop with default
  export let userName = 'User';
  
  // Subscribe to liveAPI store for model content
  let unsubscribe = liveAPI.subscribe((state) => {
    // If we have new content from the model and are in loading state
    if (state.latestModelContent && isLoading) {
      // Extract text from content parts
      const textParts = state.latestModelContent
        .filter(part => 'text' in part)
        .map(part => (part as any).text || '')
        .join(' ');
      
      if (textParts) {
        modelMessage = textParts;
        
        // If this is our first content for this message
        if (!messages.some(m => m.role === 'assistant' && m.id === 'current')) {
          // Add a new assistant message
          messages = [...messages, {
            id: 'current',
            role: 'assistant',
            parts: [{ type: 'text', text: modelMessage }]
          }];
        } else {
          // Update the existing message
          messages = messages.map(m => 
            m.id === 'current' 
              ? { ...m, parts: [{ type: 'text', text: modelMessage }] }
              : m
          );
        }
      }
    }
  });
  
  // Clean up subscription on component destroy
  onDestroy(() => {
    if (unsubscribe) unsubscribe();
    if (mediaUnsubscribe) mediaUnsubscribe();
  });
  
  // Generate user initials from name
  const getUserInitials = () => {
    return userName
      .split(" ")
      .map((name) => name?.[0] || "")
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Function to handle input changes
  function handleInputChange(event: Event) {
    input = (event.target as HTMLInputElement).value;
  }
  
  // Handle receiving a message from speech recognition
  function handleSpeechMessage(text: string) {
    if (!text.trim() || isLoading) return;
    
    // Process the text message just like a typed message
    processUserMessage(text.trim());
  }
  
  // Function to handle submission
  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!input.trim() || isLoading) return;
    
    // Process the user message
    processUserMessage(input.trim());
    
    // Clear input
    input = '';
  }
  
  // Process a user message
  async function processUserMessage(text: string) {
    // Add user message
    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      parts: [{ type: 'text', text }]
    };
    
    messages = [...messages, userMessage];
    isLoading = true;
    modelMessage = '';
    
    // Store a reference to this specific conversation round
    const messageTimestamp = Date.now();
    
    try {
      // Check if live API is connected
      const currentStore = get(liveAPI);
      
      if (currentStore.connected) {
        // Connected to API, send message
        await currentStore.sendMessage(text);
        
        // Response will be handled by the subscription to the liveAPI store
      } else {
        // Fall back to regular API
        try {
          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message: text })
          });
          
          const data = await response.json();
          
          // Add assistant message
          messages = [...messages, {
            id: (Date.now() + 1).toString(),
            role: 'assistant',
            parts: [{ type: 'text', text: data.text || "I'm here to help with your learning!" }]
          }];
        } catch (apiError) {
          console.error('API request failed:', apiError);
          messages = [...messages, {
            id: (Date.now() + 1).toString(),
            role: 'assistant',
            parts: [{ type: 'text', text: "Sorry, I couldn't reach the API at the moment." }]
          }];
        }
        
        isLoading = false;
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message
      messages = [...messages, {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        parts: [{ type: 'text', text: "Sorry, I couldn't process your request at the moment." }]
      }];
      
      isLoading = false;
    }
  }
  
  // Handle when a response is complete
  function handleResponseComplete() {
    if (isLoading && modelMessage) {
      // Replace the temporary ID with a permanent one
      messages = messages.map(m => 
        m.id === 'current' 
        ? { ...m, id: Date.now().toString() }
        : m
      );
      
      isLoading = false;
      modelMessage = '';
    }
  }
  
  // Scroll to bottom when messages change
  $: if (messages.length > 0) {
    setTimeout(() => {
      const chatContainer = document.getElementById("chat-messages-container");
      if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
      }
    }, 0);
  }
  
  // Ensure we scroll on mount too
  onMount(() => {
    const chatContainer = document.getElementById("chat-messages-container");
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight;
    }
  });
</script>

<div class="flex flex-col h-full bg-transparent">
  <!-- Messages area -->
  
  <div 
    id="chat-messages-container"
    class="flex-1 overflow-y-auto py-4 space-y-4 px-4 sm:px-6 md:px-32"
  >
    {#if messages.length === 0}
    
      <div class="flex items-center justify-center h-full relative">
        <div class="text-center relative">
          <img
            src="/mavialogo.png"
            alt="Mavia"
            width="64"
            height="64"
            class="mx-auto mb-4 relative"
          />
          <p class="text-foreground font-medium relative z-10">
            How can I help you with your learning today?
          </p>
        </div>
      </div>
    {:else}
      {#each messages as message (message.id)}
        <div class="w-full relative mb-4">
          {#if message.role === "user"}
            <!-- User message - left aligned with avatar first -->
            <div class="w-full flex justify-start">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-semibold">
                  {getUserInitials()}
                </div>
                <div class="rounded-lg py-2 px-4 ml-0">
                  {#each message.parts as part, i}
                    {#if part.type === 'text'}
                      <p>{part.text}</p>
                    {/if}
                  {/each}
                </div>
              </div>
            </div>
          {:else}
            <!-- Assistant message - left aligned -->
            <div class="w-full flex justify-start">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center">
                  <img
                    src="/mavialogo.png"
                    alt="Mavia"
                    width="40"
                    height="40"
                    class="object-contain"
                  />
                </div>
                <div class="rounded-lg py-2 px-4 ml-0">
                  {#each message.parts as part, i}
                    {#if part.type === 'text'}
                      <p>{part.text}</p>
                    {/if}
                  {/each}
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/each}
    {/if}
    
    {#if isLoading}
      <div class="w-full relative mb-8">
        <div class="w-full flex justify-start">
          <div class="flex items-start gap-3">
            <div class="flex-shrink-0 flex items-center justify-center">
              <img
                src="/mavialogo.png"
                alt="Mavia"
                width="40"
                height="40"
                class="object-contain"
              />
            </div>
            <div class="rounded-lg py-2 px-4 ml-0">
              <div class="flex space-x-2 items-center">
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce"></div>
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style="animation-delay:0.2s"></div>
                <div class="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style="animation-delay:0.4s"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
  
  <!-- Video Display for webcam and screen sharing -->
  {#if hasActiveMedia}
    <div class="px-4 sm:px-6 md:px-32 mb-4">
      <VideoDisplay containerClass="video-display-container" />
    </div>
  {/if}
  
  <!-- Control Tray for voice input & live API controls -->
  <div class="px-4 sm:px-6 md:px-32 mb-2">
    <ControlTray 
      onSendMessage={handleSpeechMessage}
    />
  </div>
  
  <!-- Input area -->
  <div class="sticky bottom-0 left-0 right-0 bg-transparent pt-0 pb-2 px-4 sm:px-6 md:px-32">
    <form on:submit={handleSubmit} class="relative">
      <input
        type="text"
        bind:value={input}
        on:input={handleInputChange}
        placeholder="Type your message..."
        class="w-full p-4.5 md:p-3 pr-10 rounded-full bg-card border border-border focus:outline-none focus:ring-2 focus:ring-primary/50 text-sm"
        disabled={isLoading}
      />
      <button
        type="submit"
        class="absolute right-2 top-1/2 -translate-y-1/2 w-7 h-7 md:w-8 md:h-8 flex items-center justify-center rounded-full bg-primary text-primary-foreground disabled:opacity-50"
        disabled={!input.trim() || isLoading}
        aria-label="Send message"
      >
        <!-- SVG for Send icon instead of importing from lucide-react -->
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-3.5 w-3.5 md:h-4 md:w-4">
          <path d="m22 2-7 20-4-9-9-4Z"></path>
          <path d="M22 2 11 13"></path>
        </svg>
      </button>
    </form>
  </div>
</div>
