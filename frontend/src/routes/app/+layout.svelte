<script lang="ts">
	import Sidepanel from '$lib/components/Sidepanel.svelte';

	let { children } = $props();
</script>

<div class="flex h-screen w-full overflow-hidden bg-base-100 text-base-content">
	<!-- Sidepanel -->
	<div class="flex-shrink-0">
		<Sidepanel />
	</div>

	<!-- Content Pane -->
	<div class="flex-1 min-w-0 p-6 overflow-y-auto transition-all duration-300 bg-base-100">
		{@render children()}
	</div>
</div>
