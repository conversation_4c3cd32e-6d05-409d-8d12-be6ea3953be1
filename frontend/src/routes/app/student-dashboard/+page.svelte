<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { page } from '$app/stores';
  import { apiClient, createWebSocketClient } from '$lib/api';
  import type { StudentResponse, ProgressResponse, SessionsListResponse, MaviaWebSocketClient } from '$lib/api';
  
  // Get user data
  $: user = $page.data.user;
  $: userId = $page.data.userId;
  
  // Component state
  let student: StudentResponse | null = null;
  let progress: ProgressResponse | null = null;
  let sessions: SessionsListResponse | null = null;
  let loading = true;
  let error = '';
  
  // WebSocket for real-time updates
  let wsClient: MaviaWebSocketClient | null = null;
  let wsConnected = false;
  
  // Chat state
  let chatMessages: Array<{id: string, role: 'user' | 'assistant', content: string, timestamp: Date}> = [];
  let chatInput = '';
  let chatLoading = false;
  
  onMount(async () => {
    if (userId) {
      await loadStudentData();
      initializeWebSocket();
    }
  });
  
  onDestroy(() => {
    if (wsClient) {
      wsClient.disconnect();
    }
  });
  
  async function loadStudentData() {
    try {
      loading = true;
      error = '';
      const studentId = `student_${userId}`;
      
      // Load student data
      try {
        student = await apiClient.getStudentDB(studentId);
      } catch (err: any) {
        if (err.message.includes('404')) {
          // Student doesn't exist, redirect to profile creation
          window.location.href = '/app/profile';
          return;
        }
        throw err;
      }
      
      // Load progress and sessions
      const [progressData, sessionsData] = await Promise.allSettled([
        apiClient.getStudentProgressDB(studentId),
        apiClient.getStudentSessionsDB(studentId, 10, 0)
      ]);
      
      if (progressData.status === 'fulfilled') {
        progress = progressData.value;
      }
      
      if (sessionsData.status === 'fulfilled') {
        sessions = sessionsData.value;
      }
      
    } catch (err: any) {
      error = `Failed to load student data: ${err.message}`;
    } finally {
      loading = false;
    }
  }
  
  function initializeWebSocket() {
    if (!userId) return;
    
    wsClient = createWebSocketClient(`student_${userId}`);
    wsClient.connect(
      (data) => {
        // Handle incoming messages
        if (data.type === 'message') {
          chatMessages = [...chatMessages, {
            id: Date.now().toString(),
            role: 'assistant',
            content: data.message,
            timestamp: new Date()
          }];
        }
        wsConnected = true;
      },
      (error) => {
        console.error('WebSocket error:', error);
        wsConnected = false;
      },
      () => {
        wsConnected = false;
      }
    );
  }
  
  async function sendChatMessage() {
    if (!chatInput.trim() || chatLoading || !wsClient) return;
    
    const userMessage = {
      id: Date.now().toString(),
      role: 'user' as const,
      content: chatInput.trim(),
      timestamp: new Date()
    };
    
    chatMessages = [...chatMessages, userMessage];
    
    // Send via WebSocket with correct student ID
    const studentId = userId ? `student_${userId}` : student?.student_id;
    wsClient.sendMessage(chatInput.trim(), studentId);
    
    chatInput = '';
    chatLoading = true;
    
    // Simulate AI response delay
    setTimeout(() => {
      chatLoading = false;
    }, 2000);
  }
  
  async function startNewSession() {
    if (!student) return;
    
    try {
      const sessionId = `session_${userId}_${Date.now()}`;
      await apiClient.createSessionDB({
        session_id: sessionId,
        student_id: student.student_id,
        target_topics: ['general_learning'],
        session_goals: ['Interactive learning session']
      });
      
      // Reload sessions
      await loadStudentData();
    } catch (err: any) {
      error = `Failed to start session: ${err.message}`;
    }
  }
  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
</script>

<div class="max-w-7xl mx-auto p-6 space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-3xl font-bold">Student Dashboard</h1>
      {#if student}
        <p class="text-lg text-gray-600">Welcome back, {student.name}!</p>
      {/if}
    </div>
    
    <div class="flex items-center gap-4">
      <!-- WebSocket Status -->
      <div class="flex items-center gap-2">
        <div class="w-3 h-3 rounded-full {wsConnected ? 'bg-green-500' : 'bg-red-500'}"></div>
        <span class="text-sm">{wsConnected ? 'Connected' : 'Disconnected'}</span>
      </div>
      
      <button class="btn btn-primary" on:click={startNewSession}>
        Start New Session
      </button>
    </div>
  </div>
  
  {#if loading}
    <div class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  {:else if error}
    <div class="alert alert-error">
      <span>{error}</span>
      <button class="btn btn-sm" on:click={loadStudentData}>Retry</button>
    </div>
  {:else}
    <!-- Main Dashboard Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      
      <!-- Left Column: Progress & Stats -->
      <div class="lg:col-span-1 space-y-6">
        
        <!-- Progress Overview -->
        {#if progress}
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h2 class="card-title">Your Progress</h2>
              
              <div class="stats stats-vertical shadow">
                <div class="stat">
                  <div class="stat-title">Total Sessions</div>
                  <div class="stat-value text-primary">{progress.total_sessions}</div>
                </div>
                
                <div class="stat">
                  <div class="stat-title">Study Time</div>
                  <div class="stat-value text-secondary">{Math.round(progress.total_time_minutes)} min</div>
                </div>
                
                <div class="stat">
                  <div class="stat-title">Achievements</div>
                  <div class="stat-value text-accent">{progress.achievements.length}</div>
                </div>
              </div>
              
              {#if progress.achievements.length > 0}
                <div class="mt-4">
                  <h3 class="font-semibold mb-2">Recent Achievements</h3>
                  <div class="flex flex-wrap gap-2">
                    {#each progress.achievements.slice(0, 3) as achievement}
                      <div class="badge badge-success">{achievement}</div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          </div>
        {/if}
        
        <!-- Student Profile Summary -->
        {#if student}
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h2 class="card-title">Profile</h2>
              
              <div class="space-y-2">
                <p><strong>Math Level:</strong> {student.math_level}</p>
                <p><strong>Grade:</strong> {student.grade_level}</p>
                <p><strong>Learning Style:</strong> {student.learning_style || 'Not specified'}</p>
              </div>
              
              {#if student.goals.length > 0}
                <div class="mt-4">
                  <h3 class="font-semibold mb-2">Current Goals</h3>
                  <div class="space-y-1">
                    {#each student.goals.slice(0, 3) as goal}
                      <div class="badge badge-primary badge-outline">{goal}</div>
                    {/each}
                  </div>
                </div>
              {/if}
              
              <div class="card-actions justify-end mt-4">
                <a href="/app/profile" class="btn btn-sm btn-outline">Edit Profile</a>
              </div>
            </div>
          </div>
        {/if}
      </div>
      
      <!-- Middle Column: Chat Interface -->
      <div class="lg:col-span-1">
        <div class="card bg-base-200 shadow-xl h-[600px] flex flex-col">
          <div class="card-body flex-1 flex flex-col">
            <h2 class="card-title">AI Tutor Chat</h2>
            
            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto space-y-4 mb-4">
              {#each chatMessages as message}
                <div class="chat {message.role === 'user' ? 'chat-end' : 'chat-start'}">
                  <div class="chat-bubble {message.role === 'user' ? 'chat-bubble-primary' : 'chat-bubble-secondary'}">
                    {message.content}
                  </div>
                  <div class="chat-footer opacity-50">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              {/each}
              
              {#if chatLoading}
                <div class="chat chat-start">
                  <div class="chat-bubble chat-bubble-secondary">
                    <span class="loading loading-dots loading-sm"></span>
                  </div>
                </div>
              {/if}
            </div>
            
            <!-- Chat Input -->
            <form on:submit|preventDefault={sendChatMessage} class="flex gap-2">
              <input 
                type="text" 
                class="input input-bordered flex-1" 
                placeholder="Ask me anything about math..."
                bind:value={chatInput}
                disabled={!wsConnected || chatLoading}
              />
              <button 
                type="submit" 
                class="btn btn-primary"
                disabled={!chatInput.trim() || !wsConnected || chatLoading}
              >
                Send
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Right Column: Recent Sessions -->
      <div class="lg:col-span-1">
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="card-title">Recent Sessions</h2>
            
            {#if sessions && sessions.sessions.length > 0}
              <div class="space-y-3">
                {#each sessions.sessions as session}
                  <div class="border border-base-300 rounded-lg p-3">
                    <div class="flex justify-between items-start mb-2">
                      <span class="font-semibold text-sm">Session {session.session_id.split('_').pop()}</span>
                      <span class="badge badge-{session.ended_at ? 'success' : 'warning'} badge-sm">
                        {session.ended_at ? 'Completed' : 'In Progress'}
                      </span>
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-2">
                      Started: {formatDate(session.started_at)}
                    </p>
                    
                    {#if session.target_topics.length > 0}
                      <div class="flex flex-wrap gap-1">
                        {#each session.target_topics.slice(0, 2) as topic}
                          <span class="badge badge-outline badge-xs">{topic}</span>
                        {/each}
                      </div>
                    {/if}
                    
                    <div class="text-xs text-gray-500 mt-2">
                      Duration: {session.time_spent_minutes} minutes
                    </div>
                  </div>
                {/each}
              </div>
            {:else}
              <div class="text-center py-8 text-gray-500">
                <p>No sessions yet</p>
                <p class="text-sm">Start your first learning session!</p>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>
