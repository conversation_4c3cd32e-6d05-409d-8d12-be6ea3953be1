<script lang="ts">
  import { onMount } from 'svelte';
  import { apiClient } from '$lib/api';
  import type { StudentsListResponse, CurriculaListResponse } from '$lib/api';
  
  // Component state
  let students: StudentsListResponse | null = null;
  let curricula: CurriculaListResponse | null = null;
  let loading = true;
  let error = '';
  let selectedTab = 'students';
  
  // Pagination
  let currentPage = 0;
  let pageSize = 20;
  
  onMount(async () => {
    await loadData();
  });
  
  async function loadData() {
    try {
      loading = true;
      error = '';
      
      const [studentsData, curriculaData] = await Promise.allSettled([
        apiClient.listStudentsDB(pageSize, currentPage * pageSize),
        apiClient.listCurriculaDB()
      ]);
      
      if (studentsData.status === 'fulfilled') {
        students = studentsData.value;
      } else {
        console.error('Failed to load students:', studentsData.reason);
      }
      
      if (curriculaData.status === 'fulfilled') {
        curricula = curriculaData.value;
      } else {
        console.error('Failed to load curricula:', curriculaData.reason);
      }
      
    } catch (err: any) {
      error = `Failed to load data: ${err.message}`;
    } finally {
      loading = false;
    }
  }
  
  async function initializeDatabase() {
    try {
      loading = true;
      await apiClient.initializeDatabase();
      await loadData();
    } catch (err: any) {
      error = `Failed to initialize database: ${err.message}`;
      loading = false;
    }
  }
  
  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  function getSubscriptionBadgeClass(status: string) {
    switch (status) {
      case 'active': return 'badge-success';
      case 'trial': return 'badge-warning';
      case 'expired': return 'badge-error';
      default: return 'badge-ghost';
    }
  }
  
  async function nextPage() {
    currentPage++;
    await loadData();
  }
  
  async function prevPage() {
    if (currentPage > 0) {
      currentPage--;
      await loadData();
    }
  }
</script>

<div class="max-w-7xl mx-auto p-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold">Admin Dashboard</h1>
      <p class="text-gray-600">Manage students, curricula, and system settings</p>
    </div>
    
    <div class="flex gap-2">
      <button class="btn btn-secondary" on:click={loadData}>
        Refresh Data
      </button>
      <button class="btn btn-warning" on:click={initializeDatabase}>
        Initialize DB
      </button>
    </div>
  </div>
  
  {#if error}
    <div class="alert alert-error mb-6">
      <span>{error}</span>
      <button class="btn btn-sm" on:click={loadData}>Retry</button>
    </div>
  {/if}
  
  <!-- Tabs -->
  <div class="tabs tabs-boxed mb-6">
    <button 
      class="tab {selectedTab === 'students' ? 'tab-active' : ''}"
      on:click={() => selectedTab = 'students'}
    >
      Students ({students?.count || 0})
    </button>
    <button 
      class="tab {selectedTab === 'curricula' ? 'tab-active' : ''}"
      on:click={() => selectedTab = 'curricula'}
    >
      Curricula ({curricula?.count || 0})
    </button>
    <button 
      class="tab {selectedTab === 'analytics' ? 'tab-active' : ''}"
      on:click={() => selectedTab = 'analytics'}
    >
      Analytics
    </button>
  </div>
  
  {#if loading}
    <div class="flex justify-center items-center h-64">
      <span class="loading loading-spinner loading-lg"></span>
    </div>
  {:else}
    
    <!-- Students Tab -->
    {#if selectedTab === 'students'}
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Students Management</h2>
          
          {#if students && students.students.length > 0}
            <!-- Students Table -->
            <div class="overflow-x-auto">
              <table class="table table-zebra">
                <thead>
                  <tr>
                    <th>Student ID</th>
                    <th>Name</th>
                    <th>Math Level</th>
                    <th>Email</th>
                    <th>Subscription</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {#each students.students as student}
                    <tr>
                      <td class="font-mono text-sm">{student.student_id}</td>
                      <td class="font-semibold">{student.name}</td>
                      <td>
                        <span class="badge badge-outline">{student.math_level}</span>
                      </td>
                      <td class="text-sm">{student.user_email}</td>
                      <td>
                        <span class="badge {getSubscriptionBadgeClass(student.subscription_status)}">
                          {student.subscription_status}
                        </span>
                      </td>
                      <td class="text-sm">{formatDate(student.created_at)}</td>
                      <td>
                        <div class="flex gap-1">
                          <button class="btn btn-xs btn-primary">View</button>
                          <button class="btn btn-xs btn-secondary">Edit</button>
                        </div>
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            <div class="flex justify-between items-center mt-4">
              <div class="text-sm text-gray-600">
                Showing {currentPage * pageSize + 1} to {Math.min((currentPage + 1) * pageSize, students.count)} of {students.count} students
              </div>
              <div class="join">
                <button 
                  class="join-item btn btn-sm" 
                  on:click={prevPage}
                  disabled={currentPage === 0}
                >
                  Previous
                </button>
                <button class="join-item btn btn-sm btn-active">
                  Page {currentPage + 1}
                </button>
                <button 
                  class="join-item btn btn-sm" 
                  on:click={nextPage}
                  disabled={students.count <= (currentPage + 1) * pageSize}
                >
                  Next
                </button>
              </div>
            </div>
          {:else}
            <div class="text-center py-8 text-gray-500">
              <p>No students found</p>
              <p class="text-sm">Students will appear here once they create profiles</p>
            </div>
          {/if}
        </div>
      </div>
    
    <!-- Curricula Tab -->
    {:else if selectedTab === 'curricula'}
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Curricula Management</h2>
          
          {#if curricula && curricula.curricula.length > 0}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {#each curricula.curricula as curriculum}
                <div class="card bg-base-100 shadow">
                  <div class="card-body">
                    <h3 class="card-title text-lg">{curriculum.name}</h3>
                    <p class="text-sm text-gray-600">{curriculum.description || 'No description'}</p>
                    
                    <div class="flex justify-between items-center mt-4">
                      <span class="badge badge-outline">v{curriculum.version}</span>
                      <span class="badge {curriculum.is_active ? 'badge-success' : 'badge-error'}">
                        {curriculum.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    
                    <div class="text-xs text-gray-500 mt-2">
                      Created: {formatDate(curriculum.created_at)}
                    </div>
                    
                    <div class="card-actions justify-end mt-4">
                      <button class="btn btn-xs btn-primary">View Topics</button>
                      <button class="btn btn-xs btn-secondary">Edit</button>
                    </div>
                  </div>
                </div>
              {/each}
            </div>
          {:else}
            <div class="text-center py-8 text-gray-500">
              <p>No curricula found</p>
              <p class="text-sm">Create curricula to organize learning content</p>
            </div>
          {/if}
        </div>
      </div>
    
    <!-- Analytics Tab -->
    {:else if selectedTab === 'analytics'}
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        <!-- System Stats -->
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="card-title">System Overview</h2>
            
            <div class="stats stats-vertical shadow">
              <div class="stat">
                <div class="stat-title">Total Students</div>
                <div class="stat-value text-primary">{students?.count || 0}</div>
              </div>
              
              <div class="stat">
                <div class="stat-title">Active Curricula</div>
                <div class="stat-value text-secondary">{curricula?.count || 0}</div>
              </div>
              
              <div class="stat">
                <div class="stat-title">Database Status</div>
                <div class="stat-value text-accent">Online</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Subscription Stats -->
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="card-title">Subscriptions</h2>
            
            {#if students}
              {@const subscriptionCounts = students.students.reduce((acc, student) => {
                acc[student.subscription_status] = (acc[student.subscription_status] || 0) + 1;
                return acc;
              }, {})}
              
              <div class="space-y-2">
                {#each Object.entries(subscriptionCounts) as [status, count]}
                  <div class="flex justify-between items-center">
                    <span class="badge {getSubscriptionBadgeClass(status)}">{status}</span>
                    <span class="font-semibold">{count}</span>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        </div>
        
        <!-- Math Level Distribution -->
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body">
            <h2 class="card-title">Math Levels</h2>
            
            {#if students}
              {@const levelCounts = students.students.reduce((acc, student) => {
                acc[student.math_level] = (acc[student.math_level] || 0) + 1;
                return acc;
              }, {})}
              
              <div class="space-y-2">
                {#each Object.entries(levelCounts) as [level, count]}
                  <div class="flex justify-between items-center">
                    <span class="badge badge-outline">{level}</span>
                    <span class="font-semibold">{count}</span>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        </div>
      </div>
    {/if}
  {/if}
</div>
