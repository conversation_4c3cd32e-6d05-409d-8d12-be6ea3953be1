<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { apiClient } from '$lib/api';
  import type { StudentResponse, ProgressResponse } from '$lib/api';

  // Get user's first name from <PERSON> via page data, or default to 'User' if undefined
  $: firstName = $page.data.user?.firstName || 'User';
  $: userId = $page.data.userId;

  // Student data
  let student: StudentResponse | null = null;
  let progress: ProgressResponse | null = null;
  let loading = true;

  // Load student data on mount
  onMount(async () => {
    if (userId) {
      await loadStudentData();
    }
  });

  async function loadStudentData() {
    try {
      loading = true;
      const studentId = `student_${userId}`;

      // Try to load student and progress data
      try {
        student = await apiClient.getStudentDB(studentId);
        progress = await apiClient.getStudentProgressDB(studentId);
      } catch (err: any) {
        if (err.message.includes('404')) {
          // Student doesn't exist yet
          student = null;
          progress = null;
        } else {
          throw err;
        }
      }
    } catch (error) {
      console.error('Failed to load student data:', error);
    } finally {
      loading = false;
    }
  }

  // Dashboard actions data structure
  const dashboardActions = [
    {
      title: "Profile",
      description: "Tell us about yourself and how you like to learn.",
      icon: "UserCircle",
      url: "/app/profile",
      enabled: true
    },
    {
      title: "Learning Goals",
      description: "Finish the introductory session to enable this card.",
      icon: "Target",
      url: "/app/learn",
      enabled: true
    },
    {
      title: "Practice Test",
      description: "Finish the introductory session to enable this card.",
      icon: "ClipboardCheck",
      url: "/app/practice",
      enabled: true
    },
    {
      title: "Learning Path",
      description: "Finish the introductory session to enable this card.",
      icon: "Route",
      url: "",
      enabled: false
    }
  ];
  
  // Function to handle card click
  function handleSectionClick(url: string) {
    if (url) {
      window.location.href = url;
    }
  }
</script>

<div class="flex flex-col h-full py-12 bg-base-100">
  <div class="flex-1 overflow-y-auto flex items-center justify-center">
    <div class="w-full max-w-5xl mx-auto px-0 md:px-6 box-border">
      <div class="flex flex-col md:flex-row items-center gap-4 md:gap-10">
        <!-- Tutor avatar - hidden on mobile -->
        <div class="flex-shrink-0 ml-[1px] lg:-mb-[19px] -mb-[14px] z-50 hidden md:block">
          <img 
            src="/homepageavatar.png" 
            alt="Mavia"
            class="object-contain w-[160px] md:w-[180px] lg:w-[240px] h-[160px] md:h-[180px] lg:h-[240px]"
            style="transform: rotateY(180deg)"
          />
        </div>
        
        <div class="flex flex-col text-center md:text-left mb-3">
          <h1 class="title text-3xl md:text-4xl lg:text-5xl mb-1 sm:mb-2 font-bold z-50">
            Hello {firstName}
          </h1>
          <p class="text-2xl md:text-3xl lg:text-4xl text-muted-foreground font-medium z-50">
            Ready to get started?
          </p>
        </div>
      </div>

      <!-- Student Progress Section -->
      {#if !loading && student && progress}
        <div class="mb-8">
          <h2 class="text-2xl font-bold mb-4">Your Progress</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">Total Sessions</div>
              <div class="stat-value text-primary">{progress.total_sessions}</div>
              <div class="stat-desc">Learning sessions completed</div>
            </div>

            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">Study Time</div>
              <div class="stat-value text-secondary">{Math.round(progress.total_time_minutes)} min</div>
              <div class="stat-desc">Total time spent learning</div>
            </div>

            <div class="stat bg-base-200 rounded-lg">
              <div class="stat-title">Achievements</div>
              <div class="stat-value text-accent">{progress.achievements.length}</div>
              <div class="stat-desc">Badges earned</div>
            </div>
          </div>
        </div>
      {/if}

      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-4 w-[98%] mx-auto sm:w-full box-border overflow-visible justify-items-center">
        {#each dashboardActions as action}
          <!-- Action Card -->
          <div 
            class="card w-full bg-base-200 shadow-xl hover:shadow-2xl transition-all cursor-pointer {!action.enabled ? 'opacity-70' : ''}"
            on:click={() => action.enabled && handleSectionClick(action.url)}
          >
            <div class="card-body">
              <h2 class="card-title">
                <!-- Simple icon representation since we don't have the actual icon components -->
                <span class="w-6 h-6 flex items-center justify-center text-primary">
                  {#if action.icon === "UserCircle"}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  {:else if action.icon === "Target"}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  {:else if action.icon === "ClipboardCheck"}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  {:else if action.icon === "Route"}
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                    </svg>
                  {/if}
                </span>
                {action.title}
              </h2>
              <p>{action.description}</p>
            </div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</div>
