<script lang="ts">
  import { onMount } from 'svelte';
  import { apiClient } from '$lib/api';

  let sessions: any[] = [];
  let loading = false;
  let error = '';

  async function testAPI() {
    loading = true;
    error = '';
    try {
      console.log('Testing API...');
      const studentId = 'student_user_2ul6SW4GjPFN46Jkkne0Gb8OyTU';
      console.log('Calling getStudentSessionsDB with:', studentId);
      
      const result = await apiClient.getStudentSessionsDB(studentId, 5, 0);
      console.log('API result:', result);

      sessions = result.sessions || [];
      console.log('Sessions:', sessions);

      // Test message counts like the sidebar does
      for (let i = 0; i < sessions.length; i++) {
        try {
          const messagesData = await apiClient.getSessionMessagesDB(sessions[i].session_id, 1, 0);
          console.log(`Session ${sessions[i].session_id} has ${messagesData.total} messages`);
          sessions[i].message_count = messagesData.total;
        } catch (msgError) {
          console.error(`Failed to get messages for session ${sessions[i].session_id}:`, msgError);
          sessions[i].message_count = 0;
        }
      }
    } catch (err) {
      console.error('API Error:', err);
      error = err instanceof Error ? err.message : String(err);
    } finally {
      loading = false;
    }
  }

  onMount(() => {
    testAPI();
  });
</script>

<div class="p-8">
  <h1 class="text-2xl font-bold mb-4">API Debug Page</h1>
  
  <button 
    class="btn btn-primary mb-4" 
    on:click={testAPI}
    disabled={loading}
  >
    {loading ? 'Loading...' : 'Test API'}
  </button>

  {#if error}
    <div class="alert alert-error mb-4">
      <span>Error: {error}</span>
    </div>
  {/if}

  <div class="mb-4">
    <h2 class="text-lg font-semibold">Sessions ({sessions.length}):</h2>
  </div>

  {#if sessions.length > 0}
    <div class="space-y-2">
      {#each sessions as session}
        <div class="card bg-base-200 p-4">
          <h3 class="font-medium">{session.session_id}</h3>
          <p class="text-sm text-base-content/70">
            Started: {new Date(session.started_at).toLocaleString()}
          </p>
          <p class="text-sm text-base-content/70">
            Phase: {session.current_phase}
          </p>
          <p class="text-sm text-base-content/70">
            Time: {session.time_spent_minutes} minutes
          </p>
          <p class="text-sm text-base-content/70">
            Messages: {session.message_count || 0}
          </p>
        </div>
      {/each}
    </div>
  {:else if !loading}
    <p class="text-base-content/70">No sessions found</p>
  {/if}
</div>
