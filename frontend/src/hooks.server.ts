import { with<PERSON>lerk<PERSON><PERSON><PERSON> } from 'svelte-clerk/server';
import { redirect, type Handle } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { PUBLIC_AUTH_REDIRECT_URL } from '$env/static/public';

// Base Clerk handler
const clerkHandler = withClerkHandler();

// Custom handler for post-authentication redirects
const redirectHandler: Handle = async ({ event, resolve }) => {
  // Check if user is authenticated and on the home page
  if (event.locals.auth?.userId && event.url.pathname === '/') {
    throw redirect(303, PUBLIC_AUTH_REDIRECT_URL);
  }
  
  return await resolve(event);
};

// Sequence the handlers
export const handle = sequence(clerk<PERSON>and<PERSON>, redirectHandler);
