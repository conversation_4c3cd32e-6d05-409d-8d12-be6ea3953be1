@import 'tailwindcss';
@import "tw-animate-css";

@plugin "daisyui";

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
  }
  
  :root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
  }
  
  .dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
  }
  
  @layer base {
    * {
      @apply border-border outline-ring/50;
    }
    body {
      @apply bg-background text-foreground;
    }
  }
  
  .body, html {
    background: #242424; 
    height:100%; 
    width:100%; 
    position:fixed;
    font-family:Inter,sans-serif;
    -webkit-overflow-scrolling: touch;  /* Improve mobile scrolling */
  }
  
  /* Mobile responsiveness improvements */
  @media (max-width: 768px) {
    /* Ensure proper touch target sizes on mobile */
    button, 
    a, 
    [role="button"],
    input[type="button"],
    input[type="submit"] {
      min-height: 44px;
      min-width: 44px;
    }
    
    /* Improve text readability on small screens */
    body {
      font-size: 16px;
      line-height: 1.5;
    }
    
    /* Ensure content doesn't overflow on mobile */
    img, 
    svg, 
    video {
      max-width: 100%;
      height: auto;
    }
  }
  
  @keyframes slide {
    0% {
      transform:translateX(-25%);
    }
    100% {
      transform:translateX(25%);
    }
  }
  
  
  .bg {
      animation:slide 12s ease-in-out infinite alternate;
      background-image: linear-gradient(-60deg,  #001b1f 50%, #000d1f 50%);
      bottom:0;
      position:fixed;
      left:-50%;
      opacity:.5;
      right:-50%;
      top:0;
      z-index:0;
      overflow:hidden;
      width:200%;
      height:200%;
    }
    
    .bg2 {
      animation-direction:alternate-reverse;
      position:fixed;
      animation-duration:16s;
      overflow:hidden;
      z-index:0;
      top:0;
    }
    
    .bg3 {
      animation-duration:20s;
      position:fixed;
      overflow:hidden;
      z-index:0;
      top:0;
    }
  
    .titlegrid {
      display: grid;
      grid-template-columns: 25% 75%;
   margin-bottom:-30px;
    }
  
    .title {
      background: radial-gradient(
        circle at 100%,
        #b2a8fd,
        #8678f9 50%,
        #c7d2fe 75%,
        #9a8dfd 75%
      );
      font-weight: 600;
      background-size: 200% auto;
      color: #000;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: animatedTextGradient 1.5s linear infinite;
    }
    
    @keyframes animatedTextGradient {
      from {
        background-position: 200% center;
      }
    }
  
    .suggestion {
      transition: 0.2s ease;
      background: rgba(0, 18, 20, 0.5);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      box-shadow: 2px 2px 3px rgba(0,0,0,0.4);
    }
    
    .suggestion:hover {
     /* background: var(--secondary-hover-color);*/
    background-image: linear-gradient(135deg, rgba(0, 18, 20, 0.8) 80%, rgba(20,70,70, 0.5));
    cursor: pointer;
    transform: perspective(1000px) rotateX(-5deg) rotateY(5deg) scale(1.01);
    }
  
    .suggestion-disabled {
      /*pointer-events: none;*/
      opacity: 0.5; 
      transition: 0.2s ease;
      background: rgba(210, 210, 210, 0.1);
      backdrop-filter: blur(5px);
      border-color:rgba(210, 210, 210, 0.5);
      -webkit-backdrop-filter: blur(5px);
      box-shadow: 2px 2px 3px rgba(0,0,0,0.4);
    }
  
    .suggestion-disabled:hover {
      animation: shake 0.2s ease-in-out;
      animation-iteration-count: 3;
  }
  
  @keyframes shake {
    0%, 100% { transform: translateX(0) scale(1.01); }
    25% { transform: translateX(-1px) scale(1.01); }
    75% { transform: translateX(1px) scale(1.01); }
  }
