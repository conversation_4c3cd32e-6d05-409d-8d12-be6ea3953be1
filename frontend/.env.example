# Backend API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000

# Gemini API Key (for backend)
VITE_GEMINI_API_KEY='YOUR_GEMINI_API_KEY'

# Clerk Authentication
PUBLIC_CLERK_PUBLISHABLE_KEY=YOUR_CLERK_PUBLISHABLE_KEY
CLERK_SECRET_KEY=YOUR_CLERK_SECRET_KEY

# App Configuration
PUBLIC_AUTH_REDIRECT_URL=/app/dashboard

# Development/Production flags
VITE_ENVIRONMENT=development
VITE_DEBUG=true

# Feature flags
VITE_ENABLE_WEBSOCKETS=true
VITE_ENABLE_STREAMING=true
VITE_ENABLE_VOICE=false
