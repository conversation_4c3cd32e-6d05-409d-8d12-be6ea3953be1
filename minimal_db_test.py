import asyncio
import asyncpg
from dotenv import load_dotenv
import os

load_dotenv()

async def test_connection():
    try:
        # Test direct asyncpg connection
        conn = await asyncpg.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password=os.getenv('DB_PASSWORD'),
            database='mavia'
        )
        print("✅ Direct asyncpg connection successful!")
        await conn.close()
        
        # Test with connection string
        database_url = os.getenv('DATABASE_URL')
        print(f"Testing URL: {database_url}")
        conn = await asyncpg.connect(database_url)
        print("✅ Connection string successful!")
        await conn.close()
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")

asyncio.run(test_connection())
