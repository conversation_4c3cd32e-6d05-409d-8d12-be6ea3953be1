#!/usr/bin/env python3
"""
Test script for Mavia Math Tutor API
Tests all endpoints to ensure they're working correctly
"""

import asyncio
import json
import time
import requests
import websockets
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000"
WS_BASE_URL = "ws://localhost:8000"

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_error(message):
    """Print error message"""
    print(f"❌ {message}")

def print_info(message):
    """Print info message"""
    print(f"ℹ️  {message}")

def test_health_check():
    """Test the health check endpoint"""
    print_header("Health Check")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print_success(f"Health check passed: {data['status']}")
            print_info(f"Timestamp: {data['timestamp']}")
            return True
        else:
            print_error(f"Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Health check error: {e}")
        return False

def test_practice_endpoint():
    """Test the practice chat endpoint"""
    print_header("Practice Chat Endpoint")
    
    try:
        payload = {
            "message": "I need help with basic algebra",
            "student_id": "test_student_001"
        }
        
        print_info("Sending practice message...")
        response = requests.post(
            f"{API_BASE_URL}/api/practice",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print_success("Practice endpoint working!")
            print_info(f"Response length: {len(data['text'])} characters")
            print_info(f"Response preview: {data['text'][:100]}...")
            return True
        else:
            print_error(f"Practice endpoint failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
    except Exception as e:
        print_error(f"Practice endpoint error: {e}")
        return False

def test_learn_endpoint():
    """Test the learn chat endpoint"""
    print_header("Learn Chat Endpoint")
    
    try:
        payload = {
            "message": "Can you explain what fractions are?",
            "student_id": "test_student_001"
        }
        
        print_info("Sending learn message...")
        response = requests.post(
            f"{API_BASE_URL}/api/learn",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print_success("Learn endpoint working!")
            print_info(f"Response length: {len(data['text'])} characters")
            print_info(f"Response preview: {data['text'][:100]}...")
            return True
        else:
            print_error(f"Learn endpoint failed: {response.status_code}")
            print_error(f"Response: {response.text}")
            return False
    except Exception as e:
        print_error(f"Learn endpoint error: {e}")
        return False

def test_streaming_endpoint():
    """Test the streaming chat endpoint"""
    print_header("Streaming Chat Endpoint")
    
    try:
        payload = {
            "message": "What is 2 + 2?",
            "student_id": "test_student_001"
        }
        
        print_info("Testing streaming response...")
        response = requests.post(
            f"{API_BASE_URL}/api/chat/stream",
            json=payload,
            headers={"Content-Type": "application/json"},
            stream=True,
            timeout=30
        )
        
        if response.status_code == 200:
            chunks_received = 0
            total_text = ""
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])
                            if data.get('done'):
                                break
                            elif data.get('text'):
                                total_text += data['text']
                                chunks_received += 1
                        except json.JSONDecodeError:
                            continue
            
            print_success("Streaming endpoint working!")
            print_info(f"Chunks received: {chunks_received}")
            print_info(f"Total text length: {len(total_text)} characters")
            print_info(f"Response preview: {total_text[:100]}...")
            return True
        else:
            print_error(f"Streaming endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Streaming endpoint error: {e}")
        return False

async def test_websocket():
    """Test the WebSocket endpoint"""
    print_header("WebSocket Chat")
    
    try:
        client_id = f"test_client_{int(time.time())}"
        uri = f"{WS_BASE_URL}/ws/chat/{client_id}"
        
        print_info(f"Connecting to WebSocket: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print_success("WebSocket connected!")
            
            # Send a test message
            test_message = {
                "message": "Hello, can you help me with math?",
                "student_id": "test_student_001"
            }
            
            print_info("Sending test message...")
            await websocket.send(json.dumps(test_message))
            
            # Receive responses
            chunks_received = 0
            total_text = ""
            
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get('type') == 'typing':
                        print_info(f"Typing indicator: {data.get('typing')}")
                    elif data.get('type') == 'message_chunk':
                        chunks_received += 1
                        total_text = data.get('full_text', '')
                    elif data.get('type') == 'message_complete':
                        print_success("WebSocket message complete!")
                        print_info(f"Chunks received: {chunks_received}")
                        print_info(f"Final text length: {len(data.get('text', ''))} characters")
                        print_info(f"Response preview: {data.get('text', '')[:100]}...")
                        break
                    elif data.get('type') == 'error':
                        print_error(f"WebSocket error: {data.get('message')}")
                        return False
                        
                except json.JSONDecodeError:
                    print_error(f"Invalid JSON received: {message}")
                    continue
            
            return True
            
    except Exception as e:
        print_error(f"WebSocket error: {e}")
        return False

def test_api_docs():
    """Test API documentation endpoints"""
    print_header("API Documentation")
    
    try:
        # Test OpenAPI docs
        response = requests.get(f"{API_BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print_success("OpenAPI docs accessible")
        else:
            print_error(f"OpenAPI docs failed: {response.status_code}")
            
        # Test ReDoc
        response = requests.get(f"{API_BASE_URL}/redoc", timeout=5)
        if response.status_code == 200:
            print_success("ReDoc documentation accessible")
        else:
            print_error(f"ReDoc failed: {response.status_code}")
            
        return True
    except Exception as e:
        print_error(f"Documentation error: {e}")
        return False

async def run_all_tests():
    """Run all API tests"""
    print_header("Mavia Math Tutor API Test Suite")
    print_info(f"Testing API at: {API_BASE_URL}")
    print_info(f"Testing WebSocket at: {WS_BASE_URL}")
    print_info(f"Test started at: {datetime.now()}")
    
    results = []
    
    # HTTP endpoint tests
    results.append(("Health Check", test_health_check()))
    results.append(("API Documentation", test_api_docs()))
    results.append(("Practice Endpoint", test_practice_endpoint()))
    results.append(("Learn Endpoint", test_learn_endpoint()))
    results.append(("Streaming Endpoint", test_streaming_endpoint()))
    
    # WebSocket test
    results.append(("WebSocket Chat", await test_websocket()))
    
    # Print summary
    print_header("Test Results Summary")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if result:
            print_success(f"{test_name}")
            passed += 1
        else:
            print_error(f"{test_name}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print_success("🎉 All tests passed! API is working correctly.")
    else:
        print_error(f"⚠️  {total - passed} tests failed. Check the logs above.")
    
    print_info(f"Test completed at: {datetime.now()}")

if __name__ == "__main__":
    print("🚀 Starting Mavia Math Tutor API Tests...")
    asyncio.run(run_all_tests())
