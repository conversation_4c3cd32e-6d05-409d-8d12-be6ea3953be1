# Mavia Math Tutor - API Integration Guide

## 🚀 **Complete Integration Overview**

This document describes the full integration between the **Svelte frontend** and **FastAPI backend** for the Mavia Math Tutor system, including streaming chat capabilities and WebSocket support.

## 📁 **Project Structure**

```
mavia-app/
├── main.py                     # FastAPI backend server
├── requirements.txt            # Backend dependencies
├── start_backend.sh           # Backend startup script
├── start_frontend.sh          # Frontend startup script
├── start_dev.sh              # Full development environment
├── frontend/                  # Svelte frontend application
│   ├── src/
│   │   ├── lib/
│   │   │   ├── api.ts         # API client and WebSocket client
│   │   │   ├── config.ts      # Configuration settings
│   │   │   └── components/
│   │   │       └── StreamingChat.svelte  # Enhanced chat component
│   │   └── routes/app/
│   │       ├── practice/+page.svelte     # Practice page with streaming
│   │       └── learn/+page.svelte        # Learn page with streaming
│   ├── vite.config.ts         # Vite config with API proxy
│   └── .env.example           # Environment variables template
└── src/                       # Existing Mavia tutor system
    └── mavia_tutor/
```

## 🔧 **Backend API (FastAPI)**

### **Core Features**
- ✅ **REST API endpoints** for chat interactions
- ✅ **WebSocket support** for real-time streaming
- ✅ **Student management** with profiles and progress
- ✅ **Session management** for tutoring sessions
- ✅ **CORS configuration** for frontend integration
- ✅ **Streaming responses** with Server-Sent Events

### **API Endpoints**

#### **Health & Documentation**
```bash
GET  /health                    # Health check
GET  /docs                      # Interactive API documentation
GET  /redoc                     # Alternative API documentation
```

#### **Chat Endpoints**
```bash
POST /api/practice              # Practice chat messages
POST /api/learn                 # Learning chat messages
POST /api/chat/stream           # Streaming chat responses (SSE)
```

#### **Student Management**
```bash
POST /api/students              # Create new student
GET  /api/students/{id}         # Get student information
```

#### **WebSocket**
```bash
WS   /ws/chat/{client_id}       # Real-time chat WebSocket
```

### **Request/Response Examples**

#### **Chat Message (POST /api/practice)**
```json
// Request
{
  "message": "I need help with fractions",
  "student_id": "student_123",
  "session_id": "session_456"
}

// Response
{
  "text": "I'd be happy to help you with fractions! Let's start with the basics...",
  "session_id": "session_456",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### **Streaming Response (POST /api/chat/stream)**
```
data: {"text": "I'd", "timestamp": "2024-01-15T10:30:00Z"}

data: {"text": "be", "timestamp": "2024-01-15T10:30:01Z"}

data: {"text": "happy", "timestamp": "2024-01-15T10:30:02Z"}

data: {"done": true}
```

#### **WebSocket Messages**
```json
// Client -> Server
{
  "message": "What is 2/3 + 1/4?",
  "student_id": "student_123"
}

// Server -> Client (Typing)
{
  "type": "typing",
  "typing": true
}

// Server -> Client (Chunk)
{
  "type": "message_chunk",
  "text": "To",
  "full_text": "To"
}

// Server -> Client (Complete)
{
  "type": "message_complete",
  "text": "To add fractions, we need a common denominator...",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🎨 **Frontend Integration (Svelte)**

### **Core Features**
- ✅ **StreamingChat component** with real-time responses
- ✅ **API client** with TypeScript support
- ✅ **WebSocket client** for live chat
- ✅ **Configuration management** with environment variables
- ✅ **Proxy setup** for development
- ✅ **Error handling** and retry logic

### **Key Components**

#### **StreamingChat.svelte**
Enhanced chat component with:
- Real-time streaming responses
- WebSocket support
- Typing indicators
- Message history
- Error handling
- Configurable chat modes

#### **API Client (lib/api.ts)**
```typescript
// HTTP API calls
await apiClient.sendPracticeMessage({
  message: "Help with algebra",
  student_id: "student_123"
});

// Streaming responses
await apiClient.streamChat(
  message,
  (chunk) => console.log(chunk),      // On chunk
  () => console.log("Complete"),       // On complete
  (error) => console.error(error)     // On error
);

// WebSocket client
const wsClient = createWebSocketClient("client_123");
wsClient.connect(onMessage, onError, onClose);
wsClient.sendMessage("Hello", "student_123");
```

### **Configuration**

#### **Environment Variables (.env)**
```bash
# Backend API
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000

# Features
VITE_ENABLE_WEBSOCKETS=true
VITE_ENABLE_STREAMING=true

# Authentication (if using Clerk)
PUBLIC_CLERK_PUBLISHABLE_KEY=your_key_here
```

#### **Vite Proxy Configuration**
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
      }
    }
  }
});
```

## 🚀 **Getting Started**

### **1. Backend Setup**
```bash
# Install dependencies
source venv/bin/activate
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your GEMINI_API_KEY

# Start backend
./start_backend.sh
# or
python main.py
```

### **2. Frontend Setup**
```bash
# Install dependencies
cd frontend
npm install

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Start frontend
./start_frontend.sh
# or
npm run dev
```

### **3. Full Development Environment**
```bash
# Start both backend and frontend
./start_dev.sh
```

### **4. Access Points**
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🔄 **Chat Flow**

### **HTTP Streaming Flow**
1. User types message in frontend
2. Frontend sends POST to `/api/practice` or `/api/learn`
3. Backend processes with Mavia tutor agent
4. Backend streams response chunks via Server-Sent Events
5. Frontend displays chunks in real-time
6. Completion signal sent when done

### **WebSocket Flow**
1. Frontend establishes WebSocket connection
2. User types message
3. Frontend sends JSON message via WebSocket
4. Backend sends typing indicator
5. Backend streams response chunks
6. Backend sends completion message
7. Frontend updates UI in real-time

## 🛠 **Development Features**

### **Hot Reload**
- ✅ Backend auto-reloads on code changes
- ✅ Frontend hot-reloads with Vite
- ✅ API proxy handles routing automatically

### **Error Handling**
- ✅ Graceful API error responses
- ✅ WebSocket reconnection logic
- ✅ Fallback messages for failures
- ✅ Loading states and indicators

### **Testing**
```bash
# Test backend health
curl http://localhost:8000/health

# Test chat endpoint
curl -X POST http://localhost:8000/api/practice \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello"}'

# Test streaming
curl -N http://localhost:8000/api/chat/stream \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello"}'
```

## 📊 **Performance Features**

### **Streaming Benefits**
- ✅ **Real-time responses** - Users see text as it's generated
- ✅ **Reduced perceived latency** - Immediate feedback
- ✅ **Better UX** - Natural conversation flow
- ✅ **Efficient bandwidth** - Chunked delivery

### **WebSocket Benefits**
- ✅ **Persistent connection** - No connection overhead
- ✅ **Bidirectional communication** - Real-time interaction
- ✅ **Lower latency** - Direct TCP connection
- ✅ **Typing indicators** - Enhanced user experience

## 🔒 **Security Features**

### **CORS Configuration**
- ✅ Configured for development and production origins
- ✅ Credentials support for authentication
- ✅ Secure headers for API requests

### **Input Validation**
- ✅ Pydantic models for request validation
- ✅ Type safety with TypeScript
- ✅ Error handling for malformed requests

## 🎯 **Next Steps**

### **Immediate Enhancements**
1. **Authentication Integration** - Connect with Clerk auth
2. **Student Context** - Use real student IDs from auth
3. **Session Persistence** - Save chat history
4. **Voice Integration** - Add speech-to-text/text-to-speech

### **Advanced Features**
1. **Multi-modal Support** - Images, equations, graphs
2. **Real-time Collaboration** - Multiple students
3. **Analytics Dashboard** - Usage metrics
4. **Mobile Optimization** - Responsive design

## 🎉 **Success Metrics**

✅ **Backend API** - Fully functional with streaming
✅ **Frontend Integration** - Seamless chat experience  
✅ **WebSocket Support** - Real-time communication
✅ **Development Environment** - Easy setup and testing
✅ **Documentation** - Comprehensive guides
✅ **Error Handling** - Robust failure recovery

The Mavia Math Tutor now has a complete, production-ready API integration with advanced streaming capabilities! 🚀
