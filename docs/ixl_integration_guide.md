# IXL Integration Guide for Mavia Math Tutor

## Overview

The Mavia Math Tutor now includes comprehensive integration with IXL Maths UK curriculum data. This integration provides access to thousands of year-specific math skills covering Reception through Year 13, significantly expanding the tutoring system's capabilities.

## Features

### 🎯 **Comprehensive Curriculum Coverage**
- **14 Year Groups**: Reception (ages 4-5) through Year 13 (ages 17-18)
- **Thousands of Skills**: Individual math skills mapped to specific year groups
- **UK Curriculum Aligned**: Content follows UK national curriculum standards
- **Progressive Difficulty**: Skills organized by complexity within each year

### 📊 **Enhanced Learning Paths**
- **Year-Specific Paths**: Learning paths tailored to each UK year group
- **Skill-Based Progression**: Granular tracking of individual math skills
- **Prerequisite Mapping**: Automatic prerequisite detection based on year progression
- **Adaptive Sequencing**: Optimal topic ordering based on dependencies

### 🔧 **Smart Integration**
- **Deduplication**: Automatically removes duplicate content when merging curricula
- **Category Mapping**: Maps IXL skills to Mavia's topic categories
- **Difficulty Assessment**: Assigns appropriate difficulty levels based on year and complexity
- **Seamless Merging**: Combines IXL data with existing Mavia curriculum

## Quick Start

### 1. Scrape IXL Curriculum

```bash
# See what would be scraped (dry run)
python cli.py scrape-curriculum --dry-run

# Scrape and save IXL data
python cli.py scrape-curriculum --output-dir ./data/ixl_curriculum

# Scrape and integrate with Mavia curriculum
python cli.py scrape-curriculum --integrate
```

### 2. Use Enhanced Curriculum

```bash
# Create a UK student
python cli.py create-student \
  --student-id uk_student_001 \
  --name "Emma Thompson" \
  --age 11 \
  --grade "Year 6" \
  --level elementary \
  --style visual

# Start tutoring with enhanced curriculum
python cli.py start-session --student-id uk_student_001
```

### 3. Python API Usage

```python
from src.mavia_tutor.main import MaviaTutor

# Initialize with integrated curriculum
tutor = MaviaTutor()  # Automatically loads integrated curriculum if available

# Create UK-specific student
student = tutor.create_student(
    student_id="year7_student",
    name="James Wilson",
    age=12,
    grade_level="Year 7",
    math_level="intermediate",
    goals=["Master Year 7 algebra", "Prepare for Year 8"]
)

# Start session - will use year-appropriate content
session_id = await tutor.start_tutoring_session(
    student_id="year7_student",
    session_goals=["Assess algebra readiness", "Practice key skills"]
)
```

## Architecture

### Scraping System

```
IXL Website → IXLScraper → Raw Data → Curriculum Converter → Mavia Format
```

**Components:**
- **IXLScraper**: Web scraper for IXL Maths UK website
- **Rate Limiting**: Respectful scraping with delays between requests
- **Data Extraction**: Extracts skills, topics, and year group information
- **Format Conversion**: Converts to Mavia curriculum format

### Integration System

```
Default Curriculum + IXL Curriculum → CurriculumIntegrator → Enhanced Curriculum
```

**Features:**
- **Deduplication**: Removes similar topics using similarity algorithms
- **Prerequisite Mapping**: Creates logical skill progressions
- **Category Assignment**: Maps skills to appropriate math categories
- **Path Optimization**: Creates efficient learning sequences

## Data Structure

### Year Group Mapping

| Year Group | Age Range | Mavia Level | Example Topics |
|------------|-----------|-------------|----------------|
| Reception | 4-5 | beginner | Counting, basic shapes |
| Year 1-2 | 5-7 | beginner | Addition, subtraction |
| Year 3-6 | 7-11 | elementary | Fractions, decimals, geometry |
| Year 7-9 | 11-14 | intermediate | Algebra, advanced geometry |
| Year 10-13 | 14-18 | advanced | Calculus, statistics |

### Topic Categories

IXL skills are automatically categorized:

- **Arithmetic**: Number operations, fractions, decimals
- **Algebra**: Variables, equations, functions
- **Geometry**: Shapes, angles, area, volume
- **Statistics**: Data analysis, probability
- **Trigonometry**: Sine, cosine, tangent

### Difficulty Levels

Skills are assigned difficulty based on:
- **Year Group**: Higher years get higher base difficulty
- **Skill Complexity**: Keywords like "advanced" increase difficulty
- **Prerequisites**: Skills with more prerequisites are harder

## Configuration

### Scraper Settings

```python
# In IXLScraper class
self.request_delay = 1.0  # Seconds between requests
self.year_groups = {...}  # Year group definitions
self.category_mapping = {...}  # Skill categorization
```

### Integration Settings

```python
# In CurriculumIntegrator class
similarity_threshold = 0.8  # For duplicate detection
max_topics_per_path = 20   # Learning path size limit
prerequisite_limit = 3     # Max prerequisites per topic
```

## Advanced Usage

### Custom Scraping

```python
from src.mavia_tutor.scrapers.ixl_scraper import IXLScraper

# Initialize scraper
scraper = IXLScraper("./custom_output")

# Scrape specific data
scraped_data = await scraper.scrape_all_years()

# Convert to Mavia format
curriculum_data = scraper.convert_to_mavia_curriculum(scraped_data)
```

### Custom Integration

```python
from src.mavia_tutor.curriculum_integration import CurriculumIntegrator

# Initialize integrator
integrator = CurriculumIntegrator("./custom_integration")

# Perform integration
integrated_curriculum = integrator.integrate_ixl_curriculum(
    "path/to/ixl_curriculum.json"
)
```

### Curriculum Analysis

```python
# Analyze integrated curriculum
curriculum = tutor.curriculum

# Count topics by category
category_counts = {}
for topic in curriculum.topics.values():
    category = topic.category.value
    category_counts[category] = category_counts.get(category, 0) + 1

print("Topics by category:", category_counts)

# Find year-specific topics
year6_topics = [
    topic for topic in curriculum.topics.values()
    if hasattr(topic, 'year_group') and 'year-6' in str(topic.year_group)
]

print(f"Year 6 topics: {len(year6_topics)}")
```

## Benefits

### For Students
- **Age-Appropriate Content**: Skills matched to UK year groups
- **Comprehensive Coverage**: Thousands of specific math skills
- **Progressive Learning**: Logical skill progression
- **Real-World Alignment**: Content matches school curriculum

### For Educators
- **Detailed Tracking**: Monitor progress on specific skills
- **Curriculum Alignment**: Follows UK national standards
- **Flexible Paths**: Multiple learning routes for different needs
- **Assessment Integration**: Built-in skill assessment

### For Developers
- **Extensible System**: Easy to add more curriculum sources
- **Clean Integration**: Seamless merging with existing content
- **Rich Metadata**: Detailed skill information and relationships
- **API Access**: Full programmatic control

## Troubleshooting

### Common Issues

**Scraping Fails**
```bash
# Check network connection
curl -I https://uk.ixl.com/maths

# Increase delay between requests
# Edit src/mavia_tutor/scrapers/ixl_scraper.py
self.request_delay = 2.0  # Increase from 1.0
```

**Integration Errors**
```bash
# Verify IXL data exists
ls -la ./data/ixl_curriculum/

# Check file format
python -c "import json; print(json.load(open('./data/ixl_curriculum/mavia_ixl_curriculum.json'))['total_topics'])"
```

**Memory Issues**
```bash
# Reduce topics per path
# Edit CurriculumIntegrator
max_topics = 10  # Reduce from 20
```

### Performance Tips

1. **Rate Limiting**: Respect IXL's servers with appropriate delays
2. **Incremental Scraping**: Scrape one year at a time for large datasets
3. **Data Caching**: Save intermediate results to avoid re-scraping
4. **Memory Management**: Process data in chunks for large curricula

## File Structure

```
data/
├── ixl_curriculum/           # Raw IXL data
│   ├── reception_skills.json
│   ├── year-1_skills.json
│   ├── ...
│   └── mavia_ixl_curriculum.json
├── integrated_curriculum/    # Merged curriculum
│   ├── integrated_curriculum.json
│   └── integration_metadata.json
└── curriculum/              # Default curriculum
    └── curriculum.json
```

## Next Steps

1. **Run the Scraper**: Get the latest IXL curriculum data
2. **Test Integration**: Verify the merged curriculum works correctly
3. **Create UK Students**: Test with year-specific student profiles
4. **Analyze Results**: Review the enhanced learning paths and topics
5. **Customize Further**: Add your own curriculum sources or modifications

The IXL integration transforms Mavia Math Tutor into a comprehensive UK curriculum-aligned tutoring system with thousands of specific skills and year-appropriate learning paths.
