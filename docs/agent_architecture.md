# Mavia Math Tutor - Agent Architecture

## Overview

The Mavia Math Tutor is a multi-agent system built using Google's Agent Development Kit (ADK) that provides personalized math tutoring through the coordination of specialized agents.

## Agent Architecture

### 1. TutorAgent (Main Orchestrator)
- **Role**: Main coordinator that orchestrates all other agents
- **Type**: LlmAgent with sub-agents
- **Responsibilities**:
  - Initialize tutoring sessions
  - Coordinate the learning flow
  - Make decisions about when to teach vs test
  - Manage session state and progress
  - Handle student interactions
  - Determine when to advance to next topics

### 2. LearningPathAgent
- **Role**: Generates personalized learning paths
- **Type**: LlmAgent
- **Responsibilities**:
  - Analyze student profile and current knowledge level
  - Generate customized learning paths based on goals and abilities
  - Recommend topic sequences
  - Adapt paths based on progress and performance

### 3. TestingAgent
- **Role**: Evaluates student knowledge and understanding
- **Type**: LlmAgent
- **Responsibilities**:
  - Generate appropriate assessments for topics
  - Evaluate student responses
  - Provide knowledge ratings (poor, basic, good, excellent)
  - Create diagnostic tests
  - Track assessment results

### 4. TopicExplainerAgent
- **Role**: Teaches math concepts and topics
- **Type**: LlmAgent
- **Responsibilities**:
  - Provide clear explanations of math concepts
  - Create examples and practice problems
  - Adapt teaching style to student preferences
  - Provide step-by-step guidance
  - Offer hints and scaffolding

## Communication Patterns

### 1. Shared Session State
All agents communicate through shared session state managed by ADK:
- `session.state['student_profile']`: Current student information
- `session.state['current_topic']`: Active topic being studied
- `session.state['learning_path']`: Generated learning path
- `session.state['knowledge_ratings']`: Topic knowledge assessments
- `session.state['session_phase']`: Current phase of tutoring

### 2. Agent Transfer (LLM-Driven Delegation)
The TutorAgent uses LLM-driven delegation to transfer control:
- Transfer to LearningPathAgent when path generation is needed
- Transfer to TestingAgent when assessment is required
- Transfer to TopicExplainerAgent when teaching is needed

### 3. Sequential Workflow
The system follows a sequential workflow pattern:
1. **Initialization**: TutorAgent starts session
2. **Path Generation**: LearningPathAgent creates learning path
3. **Topic Loop**:
   - TestingAgent assesses current topic knowledge
   - If knowledge is insufficient → TopicExplainerAgent teaches
   - If knowledge is sufficient → advance to next topic
4. **Session Completion**: TutorAgent ends session

## Workflow Diagram

```
TutorAgent (Orchestrator)
    ├── Initialize Session
    ├── Call LearningPathAgent → Generate Learning Path
    └── For each topic in path:
        ├── Call TestingAgent → Assess Knowledge
        ├── If needs learning:
        │   ├── Call TopicExplainerAgent → Teach Topic
        │   └── Call TestingAgent → Re-assess
        └── If mastered → Next Topic
```

## Agent Interactions

### TutorAgent → LearningPathAgent
- **Trigger**: Session start or path adaptation needed
- **Data**: Student profile, goals, current progress
- **Response**: Customized learning path with topic sequence

### TutorAgent → TestingAgent
- **Trigger**: Topic assessment needed
- **Data**: Topic ID, student ID, difficulty level
- **Response**: Knowledge rating and assessment results

### TutorAgent → TopicExplainerAgent
- **Trigger**: Student needs to learn a topic
- **Data**: Topic details, student learning style, knowledge gaps
- **Response**: Teaching completion status

### TestingAgent → TopicExplainerAgent (via TutorAgent)
- **Data Flow**: Assessment results inform teaching approach
- **Adaptation**: Explainer adapts based on specific knowledge gaps

## State Management

### Session State Structure
```python
{
    "session_id": "unique_session_id",
    "student_id": "student_identifier", 
    "current_phase": "topic_assessment|topic_explanation|...",
    "current_topic_id": "current_topic",
    "learning_path": {...},
    "knowledge_ratings": {...},
    "agent_states": {...}
}
```

### Agent State Tracking
Each agent maintains its state:
- `IDLE`: Ready for tasks
- `ACTIVE`: Currently processing
- `WAITING`: Waiting for input/other agents
- `COMPLETED`: Task completed
- `ERROR`: Error state

## Error Handling and Recovery

### Agent Failure Recovery
- Each agent has timeout mechanisms
- TutorAgent can retry failed operations
- Fallback strategies for each agent type
- Session state preservation for recovery

### Validation and Constraints
- Input validation at each agent boundary
- State consistency checks
- Progress validation before advancement
- Safety checks for infinite loops

## Extensibility

### Adding New Agents
- Follow ADK agent patterns
- Implement proper state management
- Add to TutorAgent's sub-agents list
- Update communication protocols

### Customization Points
- Learning path generation algorithms
- Assessment question generation
- Teaching strategies and explanations
- Student progress tracking metrics
