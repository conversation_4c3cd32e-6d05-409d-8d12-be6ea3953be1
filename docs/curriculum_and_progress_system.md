# Curriculum Storage, Retrieval & Progress Tracking System

## 📚 **Curriculum Storage Architecture**

### **1. Multi-Layer Storage Structure**

The Mavia Math Tutor uses a **hierarchical storage system** with three distinct layers:

```
data/
├── ixl_curriculum/              # Layer 1: Raw IXL Data
│   ├── reception_skills.json    # Individual year groups
│   ├── year-1_skills.json       # 4,728 total skills
│   ├── ...
│   ├── year-13_skills.json
│   ├── ixl_curriculum_complete.json     # Combined raw data
│   └── mavia_ixl_curriculum.json        # Mavia-formatted IXL data
├── curriculum/                  # Layer 2: Default Curriculum
│   └── curriculum.json          # 8 core Mavia topics
├── integrated_curriculum/       # Layer 3: Final Merged Curriculum
│   ├── integrated_curriculum.json      # 37 topics (8 Mavia + 29 IXL)
│   └── integration_metadata.json       # Integration statistics
├── students/                    # Student Data
│   └── {student_id}.json        # Individual student profiles & progress
└── sessions/                    # Session Data
    └── {session_id}.json        # Individual session records
```

### **2. Data Models & Validation**

The system uses **Pydantic models** for type safety and data validation:

#### **Topic Model**
```python
class Topic(BaseModel):
    topic_id: str                    # Unique identifier
    name: str                        # Human-readable name
    description: str                 # Detailed description
    category: TopicCategory          # arithmetic, algebra, geometry, etc.
    difficulty: Difficulty           # very_easy to very_hard
    prerequisites: List[str]         # Required prerequisite topic IDs
    learning_objectives: List[str]   # What students will learn
    key_concepts: List[str]          # Core concepts to master
    estimated_time_minutes: int     # Expected learning time
    examples: List[str]              # Example problems
```

#### **Learning Path Model**
```python
class LearningPath(BaseModel):
    path_id: str                     # Unique identifier
    name: str                        # Path name
    description: str                 # Path description
    target_level: str                # beginner, elementary, intermediate, advanced
    topic_sequence: List[str]        # Ordered topic IDs
    estimated_duration_hours: float # Total estimated time
    prerequisites: List[str]         # Path prerequisites
```

#### **Curriculum Model**
```python
class Curriculum(BaseModel):
    curriculum_id: str               # Curriculum identifier
    name: str                        # Curriculum name
    description: str                 # Curriculum description
    topics: Dict[str, Topic]         # Topics indexed by ID
    learning_paths: Dict[str, LearningPath]  # Paths indexed by ID
```

### **3. Storage Formats**

#### **IXL Topic Example**
```json
{
  "ixl_y6_convert_between_standard_and_expanded_form": {
    "topic_id": "ixl_y6_convert_between_standard_and_expanded_form",
    "name": "Convert between standard and expanded form",
    "description": "IXL Year 6 skill: Convert between standard and expanded form",
    "category": "arithmetic",
    "difficulty": "medium",
    "prerequisites": [],
    "learning_objectives": [
      "Master convert between standard and expanded form",
      "Apply convert between standard and expanded form in various contexts"
    ],
    "key_concepts": ["Mathematical reasoning", "Problem solving"],
    "estimated_time_minutes": 30,
    "examples": [],
    "source": "IXL",
    "source_url": "https://uk.ixl.com/maths/year-6/convert-between-standard-and-expanded-form",
    "year_group": "year-6",
    "age_range": "10-11",
    "level": "elementary"
  }
}
```

## 🔄 **Curriculum Retrieval System**

### **1. Priority-Based Loading**

The system uses a **cascading priority system** for curriculum loading:

```python
def _load_curriculum(self) -> Curriculum:
    # Priority 1: Integrated Curriculum (IXL + Mavia)
    if integrated_curriculum_exists():
        return load_integrated_curriculum()
    
    # Priority 2: Standard Curriculum
    if standard_curriculum_exists():
        return load_standard_curriculum()
    
    # Priority 3: Default Curriculum (fallback)
    return create_default_curriculum()
```

### **2. Curriculum Loader Class**

```python
class CurriculumLoader:
    def __init__(self, curriculum_path: str = "./data/curriculum"):
        self.curriculum_path = Path(curriculum_path)
    
    def load_curriculum(self, filename: str = "curriculum.json") -> Curriculum:
        # Load and validate curriculum from JSON
        
    def save_curriculum(self, curriculum: Curriculum, filename: str = "curriculum.json") -> bool:
        # Save curriculum with validation
        
    def load_default_curriculum(self) -> Curriculum:
        # Generate default curriculum programmatically
```

### **3. Integration Process**

The **CurriculumIntegrator** merges multiple curriculum sources:

```python
class CurriculumIntegrator:
    def integrate_ixl_curriculum(self, ixl_data_path: str) -> Curriculum:
        # 1. Load default Mavia curriculum
        default_curriculum = self.load_default_curriculum()
        
        # 2. Load IXL curriculum data
        ixl_data = self.load_ixl_data(ixl_data_path)
        
        # 3. Merge with deduplication
        integrated = self.merge_curricula(default_curriculum, ixl_data)
        
        # 4. Optimize learning paths
        enhanced_paths = self.create_enhanced_paths(integrated)
        
        # 5. Save integrated curriculum
        self.save_integrated_curriculum(integrated)
        
        return integrated
```

## 🎓 **Learning Session Flow**

### **1. Session Initialization**

```python
async def start_tutoring_session(
    self,
    student_id: str,
    target_topics: Optional[List[str]] = None,
    max_duration_minutes: int = 60,
    session_goals: Optional[List[str]] = None
) -> str:
    # 1. Retrieve student profile and progress
    student = self.student_manager.get_student(student_id)
    
    # 2. Create session with curriculum context
    session = self.session_manager.create_session(
        student=student,
        target_topics=target_topics,
        max_duration_minutes=max_duration_minutes
    )
    
    # 3. Prepare session state with curriculum data
    session_state = {
        "student_profile": student.profile.__dict__,
        "student_progress": student.progress.__dict__,
        "curriculum_topics": self.curriculum.topics,
        "learning_paths": self.curriculum.learning_paths,
        "session_goals": session_goals
    }
    
    # 4. Run multi-agent tutoring session
    await self._run_tutoring_session(session.session_id, session_state)
    
    return session.session_id
```

### **2. Curriculum Usage in Agents**

#### **Learning Path Agent**
```python
def generate_learning_path(self, student_data, current_progress, session_goals):
    # 1. Analyze student's mastery level
    mastered_topics = self.get_mastered_topics(current_progress)
    
    # 2. Find available topics (prerequisites met)
    available_topics = self.curriculum.get_available_topics(mastered_topics)
    
    # 3. Filter by student's math level
    level_appropriate = self.filter_by_level(available_topics, student_data["math_level"])
    
    # 4. Create optimized sequence
    topic_sequence = self.optimize_sequence(level_appropriate, session_goals)
    
    return {
        "path_id": f"custom_{student_data['student_id']}_{timestamp}",
        "topic_sequence": topic_sequence,
        "estimated_duration": self.calculate_duration(topic_sequence)
    }
```

#### **Topic Selection Logic**
```python
def get_available_topics(self, mastered_topics: Set[str]) -> List[Topic]:
    available = []
    for topic in self.topics.values():
        # Check if prerequisites are met
        if topic.has_prerequisites_met(mastered_topics):
            available.append(topic)
    return available

def has_prerequisites_met(self, mastered_topics: Set[str]) -> bool:
    return all(prereq in mastered_topics for prereq in self.prerequisites)
```

## 📊 **Student Progress Tracking System**

### **1. Progress Data Models**

#### **Topic Progress**
```python
class TopicProgress(BaseModel):
    topic_id: str                    # Topic identifier
    topic_name: str                  # Topic name
    mastery_level: float             # 0.0-1.0 mastery score
    attempts: int                    # Number of practice attempts
    correct_answers: int             # Number of correct responses
    last_practiced: datetime         # Last practice session
    time_spent_minutes: float        # Total time on topic
```

#### **Learning Progress**
```python
class LearningProgress(BaseModel):
    student_id: str                  # Student identifier
    current_path_id: str             # Active learning path
    topics_progress: Dict[str, TopicProgress]  # Progress by topic
    total_sessions: int              # Total tutoring sessions
    total_time_minutes: float        # Total learning time
    achievements: List[str]          # Earned achievements
    last_session: datetime           # Last session date
```

### **2. Progress Update Mechanism**

```python
def update_topic_progress(
    self,
    student_id: str,
    topic_id: str,
    topic_name: str,
    correct: bool,
    time_spent: float
) -> bool:
    student = self.get_student(student_id)
    
    # Update topic-specific progress
    if topic_id not in student.progress.topics_progress:
        student.progress.topics_progress[topic_id] = TopicProgress(
            topic_id=topic_id,
            topic_name=topic_name
        )
    
    progress = student.progress.topics_progress[topic_id]
    progress.attempts += 1
    if correct:
        progress.correct_answers += 1
    progress.time_spent_minutes += time_spent
    progress.last_practiced = datetime.now()
    
    # Calculate mastery level
    accuracy = progress.correct_answers / progress.attempts
    progress.mastery_level = min(1.0, accuracy * 1.2)  # Boost for performance
    
    # Update overall progress
    student.progress.total_time_minutes += time_spent
    student.progress.last_session = datetime.now()
    
    # Persist changes
    self._save_student(student)
    
    return True
```

### **3. Mastery Calculation**

The system uses a **sophisticated mastery algorithm**:

```python
def calculate_mastery_level(self, progress: TopicProgress) -> float:
    if progress.attempts == 0:
        return 0.0
    
    # Base accuracy
    accuracy = progress.correct_answers / progress.attempts
    
    # Time factor (faster = better mastery)
    avg_time = progress.time_spent_minutes / progress.attempts
    time_factor = max(0.8, min(1.2, 30 / avg_time))  # Optimal ~30min per attempt
    
    # Recency factor (recent practice = better retention)
    days_since = (datetime.now() - progress.last_practiced).days
    recency_factor = max(0.7, 1.0 - (days_since * 0.05))
    
    # Combined mastery score
    mastery = accuracy * time_factor * recency_factor
    
    return min(1.0, mastery)
```

### **4. Progress Analytics**

```python
def get_student_analytics(self, student_id: str) -> Dict[str, Any]:
    student = self.get_student(student_id)
    
    # Calculate statistics
    total_topics = len(student.progress.topics_progress)
    mastered_topics = sum(1 for p in student.progress.topics_progress.values() 
                         if p.mastery_level >= 0.8)
    
    # Learning velocity
    if student.progress.total_sessions > 0:
        topics_per_session = total_topics / student.progress.total_sessions
        time_per_topic = student.progress.total_time_minutes / total_topics if total_topics > 0 else 0
    else:
        topics_per_session = 0
        time_per_topic = 0
    
    # Difficulty distribution
    difficulty_progress = {}
    for topic_id, progress in student.progress.topics_progress.items():
        topic = self.curriculum.get_topic(topic_id)
        if topic:
            difficulty = topic.difficulty.value
            if difficulty not in difficulty_progress:
                difficulty_progress[difficulty] = {"total": 0, "mastered": 0}
            difficulty_progress[difficulty]["total"] += 1
            if progress.mastery_level >= 0.8:
                difficulty_progress[difficulty]["mastered"] += 1
    
    return {
        "total_topics_practiced": total_topics,
        "mastered_topics": mastered_topics,
        "mastery_percentage": (mastered_topics / total_topics * 100) if total_topics > 0 else 0,
        "total_time_hours": student.progress.total_time_minutes / 60,
        "average_session_time": student.progress.total_time_minutes / student.progress.total_sessions if student.progress.total_sessions > 0 else 0,
        "topics_per_session": topics_per_session,
        "time_per_topic": time_per_topic,
        "difficulty_breakdown": difficulty_progress,
        "recent_activity": student.progress.last_session,
        "achievements": student.progress.achievements
    }
```

## 🔄 **Complete Session Flow**

### **Step-by-Step Process**

1. **📚 Curriculum Loading**
   - Load integrated curriculum (37 topics: 8 Mavia + 29 IXL)
   - Validate data models and relationships
   - Cache in memory for session use

2. **👤 Student Retrieval**
   - Load student profile from `data/students/{student_id}.json`
   - Parse progress data and calculate current mastery levels
   - Identify mastered topics (≥80% mastery)

3. **🎯 Topic Selection**
   - Filter available topics by prerequisites
   - Consider student's math level and learning style
   - Apply curriculum logic for optimal sequencing

4. **🛤️ Learning Path Generation**
   - Use Learning Path Agent to create custom sequence
   - Consider session goals and time constraints
   - Optimize for student's strengths and challenges

5. **🎓 Session Execution**
   - Present selected topics through multi-agent system
   - Track time spent and performance on each topic
   - Adapt difficulty based on real-time performance

6. **📊 Progress Update**
   - Calculate new mastery levels based on performance
   - Update topic-specific progress metrics
   - Recalculate overall learning statistics

7. **💾 Data Persistence**
   - Save updated student progress to JSON file
   - Log session data for analytics
   - Update curriculum usage statistics

8. **🔄 Next Session Preparation**
   - Identify next recommended topics
   - Update learning path progress
   - Prepare personalized recommendations

This comprehensive system ensures that every learning session is **personalized**, **curriculum-aligned**, and **progress-tracked**, creating an optimal learning experience for each student.
