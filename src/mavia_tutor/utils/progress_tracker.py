"""
Progress tracking utilities for the multi-agent tutoring system.

This module provides functions to update progress tables during tutoring sessions,
ensuring that curriculum, topics, topic_progress, learning_progress, and learning_paths
tables are properly populated and maintained.
"""

import logging
from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import datetime
from uuid import UUID

from ..database.services.progress_service import ProgressService, TopicProgressService
from ..database.services.curriculum_service import CurriculumService
from ..database.services.student_service import StudentService

logger = logging.getLogger(__name__)


class ProgressTracker:
    """Tracks and updates student progress during tutoring sessions."""
    
    def __init__(self, db_session):
        """Initialize progress tracker with database session."""
        self.db_session = db_session
        self.progress_service = ProgressService(db_session)
        self.topic_progress_service = TopicProgressService(db_session)
        self.curriculum_service = CurriculumService(db_session)
        self.student_service = StudentService(db_session)
    
    async def start_session_tracking(
        self, 
        student_id: UUID, 
        session_id: str,
        target_topics: List[str] = None
    ) -> None:
        """Initialize progress tracking for a new session."""
        try:
            logger.info(f"Starting progress tracking for session {session_id}")
            
            # Ensure student has learning progress record
            await self.progress_service.create_or_update_progress(
                student_id=student_id,
                total_sessions=0,  # Will be incremented when session ends
                total_time_minutes=Decimal('0.0'),
                achievements=[]
            )
            
            # Initialize topic progress for target topics
            if target_topics:
                await self._initialize_topic_progress(student_id, target_topics)
            
            logger.info(f"Progress tracking initialized for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to start session tracking: {e}")
            raise
    
    async def update_topic_progress(
        self,
        student_id: UUID,
        topic_id: str,
        mastery_level: Optional[float] = None,
        correct_answers: Optional[int] = None,
        time_spent_minutes: Optional[float] = None,
        assessment_score: Optional[float] = None
    ) -> None:
        """Update progress for a specific topic during the session."""
        try:
            # Get topic name from curriculum
            topic_name = await self._get_topic_name(topic_id)
            
            # Calculate mastery level if not provided
            if mastery_level is None and assessment_score is not None:
                mastery_level = min(1.0, assessment_score / 100.0)
            
            # Update topic progress
            await self.topic_progress_service.update_topic_progress(
                student_id=student_id,
                topic_id=topic_id,
                topic_name=topic_name,
                mastery_level=Decimal(str(mastery_level)) if mastery_level else None,
                correct_answers=correct_answers,
                time_spent_minutes=Decimal(str(time_spent_minutes)) if time_spent_minutes else None
            )
            
            logger.info(f"Updated topic progress: {topic_name} for student {student_id}")
            
        except Exception as e:
            logger.error(f"Failed to update topic progress: {e}")
            raise
    
    async def complete_topic(
        self,
        student_id: UUID,
        topic_id: str,
        final_mastery_level: float,
        session_time_minutes: float
    ) -> None:
        """Mark a topic as completed with final mastery level."""
        try:
            await self.update_topic_progress(
                student_id=student_id,
                topic_id=topic_id,
                mastery_level=final_mastery_level,
                time_spent_minutes=session_time_minutes
            )
            
            # Check if student achieved mastery (>= 0.8)
            if final_mastery_level >= 0.8:
                await self._add_achievement(student_id, f"Mastered {topic_id}")
            
            logger.info(f"Topic {topic_id} completed with mastery {final_mastery_level}")
            
        except Exception as e:
            logger.error(f"Failed to complete topic: {e}")
            raise
    
    async def end_session_tracking(
        self,
        student_id: UUID,
        session_id: str,
        total_session_time_minutes: float,
        topics_covered: List[str] = None
    ) -> None:
        """Finalize progress tracking when session ends."""
        try:
            logger.info(f"Ending progress tracking for session {session_id}")
            
            # Update overall learning progress
            await self.progress_service.increment_session_stats(
                student_id=student_id,
                session_time_minutes=Decimal(str(total_session_time_minutes))
            )
            
            # Update learning path if needed
            await self._update_learning_path_progress(student_id, topics_covered or [])
            
            # Commit changes
            await self.db_session.commit()
            
            logger.info(f"Progress tracking completed for session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to end session tracking: {e}")
            await self.db_session.rollback()
            raise
    
    async def suggest_next_topics(self, student_id: UUID) -> List[str]:
        """Suggest next topics based on student's progress and learning path."""
        try:
            # Get student's current learning path
            learning_progress = await self.progress_service.get_student_progress(student_id)
            if not learning_progress or not learning_progress.current_path_id:
                return []
            
            # Get learning path details
            learning_path = await self.curriculum_service.get_by_path_id(learning_progress.current_path_id)
            if not learning_path:
                return []
            
            # Get student's topic progress
            topic_progress_list = await self.topic_progress_service.get_student_all_topic_progress(student_id)
            mastered_topics = {
                tp.topic_id for tp in topic_progress_list 
                if tp.mastery_level >= Decimal('0.8')
            }
            
            # Find next topics in the learning path
            next_topics = []
            for topic_id in learning_path.topic_sequence:
                if topic_id not in mastered_topics:
                    next_topics.append(topic_id)
                    if len(next_topics) >= 3:  # Suggest up to 3 topics
                        break
            
            return next_topics
            
        except Exception as e:
            logger.error(f"Failed to suggest next topics: {e}")
            return []
    
    async def _initialize_topic_progress(self, student_id: UUID, topic_ids: List[str]) -> None:
        """Initialize topic progress records for new topics."""
        for topic_id in topic_ids:
            if topic_id == 'general_learning':
                continue
            
            # Check if progress already exists
            existing = await self.topic_progress_service.get_student_topic_progress(student_id, topic_id)
            if not existing:
                topic_name = await self._get_topic_name(topic_id)
                await self.topic_progress_service.update_topic_progress(
                    student_id=student_id,
                    topic_id=topic_id,
                    topic_name=topic_name,
                    mastery_level=Decimal('0.0'),
                    correct_answers=0,
                    time_spent_minutes=Decimal('0.0')
                )
    
    async def _get_topic_name(self, topic_id: str) -> str:
        """Get topic name from curriculum, with fallback to formatted topic_id."""
        try:
            # Get all curricula and search for topic
            curricula = await self.curriculum_service.get_active_curricula()
            for curriculum in curricula:
                topics = await self.curriculum_service.get_curriculum_topics(curriculum.id)
                for topic in topics:
                    if topic.topic_id == topic_id:
                        return topic.name
            
            # Fallback to formatted topic_id
            return topic_id.replace('_', ' ').title()
            
        except Exception as e:
            logger.warning(f"Failed to get topic name for {topic_id}: {e}")
            return topic_id.replace('_', ' ').title()
    
    async def _add_achievement(self, student_id: UUID, achievement: str) -> None:
        """Add an achievement to student's learning progress."""
        try:
            learning_progress = await self.progress_service.get_student_progress(student_id)
            if learning_progress:
                current_achievements = learning_progress.achievements or []
                if achievement not in current_achievements:
                    current_achievements.append(achievement)
                    await self.progress_service.create_or_update_progress(
                        student_id=student_id,
                        achievements=current_achievements
                    )
        except Exception as e:
            logger.warning(f"Failed to add achievement: {e}")
    
    async def _update_learning_path_progress(self, student_id: UUID, topics_covered: List[str]) -> None:
        """Update learning path progress based on topics covered in session."""
        try:
            learning_progress = await self.progress_service.get_student_progress(student_id)
            if not learning_progress:
                return
            
            # If no current path, suggest one based on student level
            if not learning_progress.current_path_id:
                student = await self.student_service.get_by_id(student_id)
                if student:
                    suggested_path = await self._suggest_learning_path(student.math_level)
                    if suggested_path:
                        await self.progress_service.create_or_update_progress(
                            student_id=student_id,
                            current_path_id=suggested_path.path_id
                        )
        except Exception as e:
            logger.warning(f"Failed to update learning path progress: {e}")
    
    async def _suggest_learning_path(self, math_level: str) -> Optional[Any]:
        """Suggest a learning path based on student's math level."""
        try:
            curricula = await self.curriculum_service.get_active_curricula()
            for curriculum in curricula:
                paths = await self.curriculum_service.get_curriculum_paths(curriculum.id)
                # Find path matching student's level
                for path in paths:
                    if path.target_level.lower() == math_level.lower():
                        return path
                # Fallback to beginner path
                for path in paths:
                    if path.target_level.lower() == 'beginner':
                        return path
            return None
        except Exception as e:
            logger.warning(f"Failed to suggest learning path: {e}")
            return None
