# Mavia Math Tutor

An AI Multi-Agent Math Tutoring System built with Google's Agent Development Kit (ADK).

## Overview

Mavia Math Tutor is a sophisticated multi-agent system that provides personalized math tutoring through the coordination of specialized AI agents. The system adapts to individual learning styles, tracks progress, and provides targeted instruction based on student needs.

## Features

- **Multi-Agent Architecture**: Specialized agents for different aspects of tutoring
- **Personalized Learning**: Adapts to individual learning styles and pace
- **Comprehensive Assessment**: Evaluates student knowledge and identifies gaps
- **Interactive Teaching**: Provides explanations, examples, and practice problems
- **Progress Tracking**: Monitors student progress and achievements
- **Flexible Curriculum**: Supports various math topics and difficulty levels
- **IXL Integration**: Access to thousands of UK curriculum-aligned math skills (Reception to Year 13)
- **Year-Specific Content**: Age-appropriate content mapped to UK year groups
- **Curriculum Scraping**: Automated extraction and integration of external curriculum data

## Architecture

The system consists of four main agents:

### 1. TutorAgent (Orchestrator)
- Coordinates all other agents
- Manages the learning flow
- Makes decisions about when to teach vs test
- Handles session state and progress

### 2. LearningPathAgent
- Generates personalized learning paths
- Analyzes student profiles and goals
- Recommends topic sequences
- Adapts paths based on progress

### 3. TestingAgent
- Evaluates student knowledge
- Creates assessments and quizzes
- Provides knowledge ratings
- Tracks assessment results

### 4. TopicExplainerAgent
- Teaches math concepts
- Provides clear explanations and examples
- Adapts teaching style to learning preferences
- Offers practice problems and guidance

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mavia-adk-new
```

2. Create a virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Set up the database:
```bash
# Option 1: Complete setup with migrations (recommended)
python scripts/setup_database_complete.py

# Option 2: Synchronous setup (alternative)
python scripts/setup_database_sync.py

# Option 3: Manual setup
# Create tables only
python scripts/create_tables_simple.py

# Run migrations to populate curriculum data
alembic upgrade head

# Initialize with sample data
python scripts/init_database_sync.py
```

### Database Setup

The application uses PostgreSQL as the database backend. The setup scripts will:

1. **Create all database tables** including:
   - `users` - User authentication and profile data
   - `students` - Student profiles and learning preferences
   - `curricula` - Curriculum definitions and metadata
   - `topics` - Individual math topics with learning objectives
   - `learning_paths` - Structured sequences of topics
   - `tutoring_sessions` - Learning session records
   - `chat_messages` - Conversation history
   - `learning_progress` - Student progress tracking
   - `topic_progress` - Topic-specific mastery levels
   - `assessments` and `test_results` - Assessment data

2. **Populate curriculum data** from the integrated curriculum file (`data/integrated_curriculum/integrated_curriculum.json`) which includes:
   - 37 math topics (8 core Mavia topics + 29 IXL-derived topics)
   - Multiple learning paths for different skill levels
   - Prerequisites and learning objectives for each topic

3. **Create sample data** for testing and development

#### Database Configuration

Set these environment variables in your `.env` file:

```bash
# Database connection
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/mavia_tutor

# For synchronous operations (migrations, setup scripts)
DATABASE_URL_SYNC=postgresql://username:password@localhost:5432/mavia_tutor
```

#### Migration Management

The project uses Alembic for database migrations:

```bash
# Create a new migration
alembic revision -m "description_of_changes"

# Apply migrations
alembic upgrade head

# Rollback migrations
alembic downgrade -1

# View migration history
alembic history
```

### Basic Usage

#### Using the CLI

1. Create a student:
```bash
python cli.py create-student \
  --student-id alice001 \
  --name "Alice Johnson" \
  --age 12 \
  --level elementary \
  --style visual \
  --goals "Learn fractions,Improve algebra"
```

2. Start a tutoring session:
```bash
python cli.py start-session \
  --student-id alice001 \
  --duration 45 \
  --goals "Practice fractions"
```

3. View student progress:
```bash
python cli.py student-info --student-id alice001
```

4. Scrape and integrate IXL curriculum (optional):
```bash
# See what would be scraped
python cli.py scrape-curriculum --dry-run

# Scrape and integrate IXL curriculum
python cli.py scrape-curriculum --integrate
```

#### Using the Python API

```python
import asyncio
from src.mavia_tutor.main import MaviaTutor

async def main():
    # Initialize the tutor system
    tutor = MaviaTutor()
    
    # Create a student
    student = tutor.create_student(
        student_id="demo_001",
        name="Demo Student",
        age=13,
        math_level="elementary",
        learning_style="visual",
        goals=["Master fractions", "Learn basic algebra"]
    )
    
    # Start a tutoring session
    session_id = await tutor.start_tutoring_session(
        student_id="demo_001",
        session_goals=["Practice fractions"],
        max_duration_minutes=30
    )
    
    print(f"Session completed: {session_id}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration

### Environment Variables

- `GOOGLE_CLOUD_PROJECT`: Your Google Cloud project ID
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account JSON
- `GEMINI_API_KEY`: Your Gemini API key
- `LOG_LEVEL`: Logging level (INFO, DEBUG, etc.)
- `STUDENT_DATA_PATH`: Path to store student data
- `CURRICULUM_DATA_PATH`: Path to curriculum files

### Curriculum Customization

The system uses a default math curriculum, but you can customize it:

```python
from src.mavia_tutor.utils import CurriculumLoader
from src.mavia_tutor.models.curriculum import Topic, TopicCategory, Difficulty

# Load curriculum
loader = CurriculumLoader()
curriculum = loader.load_default_curriculum()

# Add custom topic
custom_topic = Topic(
    topic_id="custom_geometry",
    name="Custom Geometry",
    description="Advanced geometric concepts",
    category=TopicCategory.GEOMETRY,
    difficulty=Difficulty.HARD,
    prerequisites=["basic_geometry"],
    learning_objectives=["Master advanced shapes", "Calculate complex areas"]
)

curriculum.topics["custom_geometry"] = custom_topic

# Save updated curriculum
loader.save_curriculum(curriculum)
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run only unit tests
pytest tests/unit/

# Run only integration tests
pytest tests/integration/

# Run with coverage
pytest --cov=src/mavia_tutor
```

## Project Structure

```
mavia-adk-new/
├── src/mavia_tutor/           # Main application code
│   ├── agents/                # Agent implementations
│   │   ├── tutor_agent.py     # Main orchestrator
│   │   ├── learning_path_agent.py
│   │   ├── testing_agent.py
│   │   └── topic_explainer_agent.py
│   ├── models/                # Data models
│   │   ├── student.py         # Student-related models
│   │   ├── curriculum.py      # Curriculum models
│   │   ├── assessment.py      # Assessment models
│   │   └── session.py         # Session models
│   ├── utils/                 # Utility modules
│   │   ├── session_manager.py
│   │   ├── curriculum_loader.py
│   │   └── student_manager.py
│   └── main.py                # Main application
├── tests/                     # Test suite
│   ├── unit/                  # Unit tests
│   ├── integration/           # Integration tests
│   └── conftest.py            # Test configuration
├── docs/                      # Documentation
├── examples/                  # Example scripts
├── cli.py                     # Command-line interface
├── main.py                    # FastAPI backend server
├── requirements.txt           # Dependencies
└── README.md                  # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For questions or issues:
- Create an issue on GitHub
- Check the documentation in the `docs/` directory
- Review the examples in the `examples/` directory

## Roadmap

- [ ] Web-based user interface
- [ ] Advanced analytics and reporting
- [ ] Support for additional subjects
- [ ] Integration with learning management systems
- [ ] Mobile application
- [ ] Voice interaction capabilities
