#!/bin/bash

# Mavia Math Tutor - Backend Startup Script

echo "🚀 Starting Mavia Math Tutor Backend..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found. Please run setup first."
    echo "Run: python -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Activate virtual environment
echo "📦 Activating virtual environment..."
source venv/bin/activate

# Install requirements if needed
if [ -f "requirements.txt" ]; then
    echo "📋 Installing requirements..."
    pip install -r requirements.txt
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your API keys"
    else
        echo "❌ .env.example not found. Please create .env file manually."
        exit 1
    fi
fi

# Check for GEMINI_API_KEY
if ! grep -q "GEMINI_API_KEY=" .env || grep -q "GEMINI_API_KEY=$" .env; then
    echo "⚠️  GEMINI_API_KEY not set in .env file"
    echo "Please add your Gemini API key to the .env file"
    echo "Example: GEMINI_API_KEY=your_api_key_here"
fi

# Start the FastAPI server
echo "🌐 Starting FastAPI server on http://localhost:8000..."
echo "📚 API Documentation: http://localhost:8000/docs"
echo "🔍 Health Check: http://localhost:8000/health"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Run the API server
python main.py
